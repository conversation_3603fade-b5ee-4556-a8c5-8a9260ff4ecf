Comment 1: In the last paragraph of page 1, the author mentions: "While MLP and Transformer-based models have achieved success in multivariate time series imputation, they often prioritize capturing the temporal characteristics of individual variables, such as periodicity, trends, and seasonality." In fact, many current MLP and Transformer-based methods have explicitly considered inter-variable correlations, such as iTransformer, TSMixer, and Crossformer. A comparative analysis between these methods and the inter-variable correlation extraction method proposed in this paper should be added.

Response: Thank you for this valuable observation. You are absolutely correct that these methods do consider inter-variable correlations, and this comparison is essential for clarifying our contribution. We have added a detailed comparative analysis in the Introduction section (page 2, third paragraph) that distinguishes our approach from iTransformer, TSMixer, and Crossformer, specifically highlighting how imputation tasks differ from forecasting scenarios in handling randomly distributed missing values.

Modifications: We added a comprehensive comparative analysis in the Introduction section, page 2, third paragraph after the correlation analysis discussion. Specific changes include: "Recent methods like iTransformer, TSMixer, and Crossformer have made notable progress in capturing inter-variable correlations through variate-centric attention, channel-mixing, and dimension-segment processing, respectively. However, these approaches face distinct challenges when applied to imputation versus forecasting scenarios. In forecasting tasks, models can leverage complete historical sequences to establish variable relationships, while imputation requires handling randomly distributed missing values where the spatial-temporal context around each missing point becomes crucial. These existing methods typically process temporal and inter-variable relationships through separate mechanisms—first extracting temporal features, then modeling cross-variable interactions—which may not optimally capture the localized dependencies essential for accurate imputation. Our approach addresses this limitation by employing a unified 2D framework that simultaneously captures both temporal evolution and spatial variable relationships, particularly focusing on the local context surrounding missing values."
