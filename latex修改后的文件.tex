%% 
%% Copyright 2019-2024 Elsevier Ltd
%% 
%% Version 2.4
%% 
%% This file is part of the 'CAS Bundle'.
%% --------------------------------------
%% 
%% It may be distributed under the conditions of the LaTeX Project Public
%% License, either version 1.2 of this license or (at your option) any
%% later version.  The latest version of this license is in
%%    http://www.latex-project.org/lppl.txt
%% and version 1.2 or later is part of all distributions of LaTeX
%% version 1999/12/01 or later.
%% 
%% The list of all files belonging to the 'CAS Bundle' is
%% given in the file `manifest.txt'.
%% 
%% Template article for cas-dc documentclass for 
%% double column output.

%\documentclass[a4paper,fleqn,longmktitle]{cas-dc}
\documentclass[a4paper,fleqn]{cas-sc}

\usepackage{graphicx}
\usepackage{float}
\usepackage{booktabs}
\usepackage{multirow}
\usepackage{placeins}
\usepackage{multicol}
\usepackage[ruled,vlined]{algorithm2e}
\usepackage{amsmath}
\usepackage{xcolor}

%\usepackage[authoryear,longnamesfirst]{natbib}
%\usepackage[authoryear]{natbib}

\usepackage[numbers,sort&compress]{natbib}

%\usepackage[authoryear,longnamesfirst]{natbib}

% 解决algorithm2e和algorithm包冲突的方法
\makeatletter
\@ifpackageloaded{algorithm2e}{
  \let\listofalgorithms\relax
}{}
\makeatother

\usepackage{algorithm}
\usepackage{algpseudocode}
% \usepackage{hyperref}
% \hypersetup{
%     colorlinks=true,
%     linkcolor=blue,
%     filecolor=blue,
%     urlcolor=blue,
%     citecolor=blue,
% }

%%%Author definitions
\def\tsc#1{\csdef{#1}{\textsc{\lowercase{#1}}\xspace}}
\tsc{WGM}
\tsc{QE}
\tsc{EP}
\tsc{PMS}
\tsc{BEC}
\tsc{DE}
%%%


\begin{document}



\let\WriteBookmarks\relax
\def\floatpagepagefraction{1}
\def\textpagefraction{.001}
\shorttitle{Efficient Time Series Imputation via Multi-Scale Grouped Parametric Convolutions}
\shortauthors{Ruochen Liu, Mingxin Teng, et~al.}



\title [mode = title]{A Multi-Scale Feature Embedding Framework Using Grouped and Parametric Convolutions for Efficient Time Series Imputation}                      


\author[1]{Ruochen Liu}
\cormark[1]  

\author[1]{Mingxin Teng}  
\author[2]{Junwei Ma}  
\author[1]{Kai Wu}  

\cortext[1]{Corresponding author: Ruochen Liu. Email: <EMAIL>.}





\affiliation[1]{organization={Key Laboratory of Intelligent Perception and Image Understanding of Ministry of Education, Xidian University},  
                city={Xian},
                postcode={710068},
                state={Shanxi},
                country={China}}
\affiliation[2]{organization={The 20th Research Institute of China Electronics Technology Group Corporation},  
                city={Xian},
                postcode={710068},
                state={Shanxi},
                country={China}}
                
                
        




\begin{abstract}
Missing value imputation is a critical challenge in multivariate time series analysis, as incomplete data significantly degrades downstream task performance. Although recent methods employing Multi-Layer Perceptron (MLP) and Transformer-based models have gained attention for capturing non-linear relationships and long-range dependencies, they primarily focus on intrinsic temporal features, such as periodicity and trends, which limits their ability to handle complex, cross-variable interactions. Additionally, these models, often utilizing either simple mappings or attention mechanisms, face challenges in balancing effectiveness and computational efficiency, especially with randomly distributed missing values. To address these limitations, we propose a two-stage network architecture, the Parametric Grouped 
Convolutional Network (PGConvNet), specifically designed for time series imputation. By expanding multivariate time series from 1D to 2D and mapping variable information into higher-dimensional channels, PGConvNet effectively captures both temporal and inter-variable dependencies. The first stage employs the Multi-Scale Grouped Convolutional Block (MSGBlock) to extract multi-scale temporal and multivariate interaction features, while the second stage, the Parametric Grouped Convolutional Block (PGCBlock), dynamically adapts to the random positioning of missing values using parametric convolutions, capturing relevant variable and temporal information around missing data points in place of traditional attention mechanisms. Extensive experiments across multiple datasets demonstrate that PGConvNet not only surpasses state-of-the-art models in accuracy and efficiency but also introduces a robust multi-dimensional convolutional paradigm for multivariate time series imputation, effectively addressing complex imputation scenarios. The source code of our proposed method is available at \url{https://github.com/Tmx158/PGConvNet}.

\end{abstract}



\begin{graphicalabstract}
\includegraphics[width=\textwidth, height=1\textheight, keepaspectratio]{figs/figure2.png}

\noindent \textbf{Graphical Abstract Overview:} This graphical abstract illustrates the novel architecture of Parametric Grouped Convolutional Network(PGConvNet), a groundbreaking framework for efficient time series imputation. The two-stage network combines Multi-Scale Grouped Convolutional Block(MSGBlock) and Parametric Grouped Convolutional Block(PGCBlock) to address the challenges of multivariate time series imputation.

\noindent \textbf{Key Highlights:}
\begin{itemize}
\item Efficiently captures multi-scale temporal and inter-variable interactions through advanced grouped convolution mechanisms, ensuring robust feature representation in complex datasets.
\item Introduces a novel parametric 2D convolution module, replacing traditional attention mechanisms by dynamically adapting to the spatial and temporal positions of missing values.
\item Combines 1D and 2D convolution in a unified hybrid framework, validated through extensive experiments as a state-of-the-art imputation model for incomplete time series data.
\end{itemize}

\end{graphicalabstract}




\begin{keywords}
Time Series Imputation \sep 2D-Inspired Temporal Processing  \sep Multi-Scale Feature Embedding \sep Grouped Parametric Convolutions  
\end{keywords}


\maketitle

\section{Introduction}
Multivariate time series data play a critical role in various fields, including finance\cite{islam2013financial}, healthcare\cite{health}, telecommunication networking\cite{Mobile}, and environmental monitoring\cite{environment}, where accurate forecasting and pattern recognition are essential for decision-making. A persistent issue in these datasets is missing values, which often occur due to sensor malfunctions, human error, or data transmission failures. Such missing values can degrade model performance, introduce bias, and reduce the reliability of downstream analyses.

Traditional methods for handling missing data often involve statistical imputation techniques, such as mean substitution, interpolation, or k-nearest neighbors (KNN), among others.\cite{interpolation,saeipourdizaj2021application,chow1971best,chan1997sequential,flornes1999direct,cabodi2011interpolation,carrizosa2013time}
While these methods are straightforward, they fail to capture the temporal dependencies and complex correlations among variables in multivariate time series, limiting their effectiveness in real-world applications. Moreover, these approaches rely on simplistic assumptions, which may not align with the complex nature of the data.  


\begin{figure}[htbp]
    \centering
    \includegraphics[width=1\textwidth, height=0.25\textheight]{figs/figure1.png}
    \caption{The figure presents the Pearson correlation coefficient heatmaps for three datasets: Weather, Electricity, and ETTh1. To ensure randomness, 96 time points were randomly sampled from each dataset, and the pairwise Pearson correlation coefficients were computed for each variable.}
    \label{fig:pearson_correlation}
\end{figure}



In recent years, deep learning techniques have been extensively applied to missing value imputation in multivariate time series. Among these approaches, MLP\cite{mlp} and Transformer-based architectures\cite{transformer} have become prominent due to their capacity to capture non-linear dependencies and complex temporal patterns. 
For example, DLinear\cite{DLinear_AAAI2023}, effectively leverages MLP to handle time series tasks by learning direct mappings between input and output spaces. Similarly, Informer\cite{informer} extends the Transformer framework by improving efficiency in processing long sequences through its attention mechanism. These models have demonstrated significant success in time series imputation tasks by capturing both temporal dependencies and multivariate relationships.

\textcolor{red}{Recent methods like iTransformer}\cite{iTransformer}\textcolor{red}{, TSMixer}\cite{TSMixer}\textcolor{red}{, and Crossformer}\cite{Crossformer} \textcolor{red}{have made notable progress in capturing inter-variable correlations through variate-centric attention, channel-mixing, and dimension-segment processing, respectively. However, these approaches face distinct challenges when applied to imputation versus forecasting scenarios. In forecasting tasks, models can leverage complete historical sequences to establish variable relationships, while imputation requires handling randomly distributed missing values where the spatial-temporal context around each missing point becomes crucial. These existing methods typically process temporal and inter-variable relationships through separate mechanisms—first extracting temporal features, then modeling cross-variable interactions—which may not optimally capture the localized dependencies essential for accurate imputation. Our approach addresses this limitation by employing a unified 2D framework that simultaneously captures both temporal evolution and spatial variable relationships, particularly focusing on the local context surrounding missing values.}

\textcolor{red}{To empirically validate the importance of spatial variable relationships in imputation tasks and motivate our design choices, we conducted correlation analysis experiments across three representative datasets—Weather, Electricity, and ETTh1—selected from our complete experimental collection to cover diverse data characteristics while avoiding visual redundancy.} \textcolor{red}{For each dataset, we randomly selected 96 time points to ensure a representative sample and calculated pairwise Pearson correlation coefficients among the variables, as shown in} Figure~\ref{fig:pearson_correlation}. The experimental results demonstrate that, despite the randomness in sampling and variation across datasets, strong inter-variable correlations persist.
Notably, the correlation heatmaps indicate that most variables exhibit stronger correlations with adjacent or neighboring variables, a characteristic consistently observed across different datasets and sampling points.  This finding is crucial for multivariate time series imputation.

Moreover, a second critical challenge arises from the random distribution of missing values in time series data. \textcolor{red}{While Transformer architectures have achieved remarkable success in forecasting tasks, imputation scenarios present distinct computational challenges due to the fundamental difference in how attention mechanisms operate. In forecasting, Transformers compute attention weights across complete sequences with fixed dimensions, enabling efficient parallel matrix operations and optimized memory access patterns. However, in imputation tasks, the attention mechanism must dynamically generate masks for missing positions, selectively compute attention weights only for observed values, and maintain gradient flow through sparse attention matrices. This requires additional computational overhead for mask generation at each layer, conditional attention computation that cannot fully utilize parallel processing capabilities, and memory allocation for sparse matrix operations. Furthermore, the backpropagation process becomes more complex as gradients must be selectively propagated through the masked attention structure, increasing both computational time and memory requirements compared to the streamlined operations possible with complete sequences in forecasting tasks.}

\textcolor{red}{To address these limitations, we propose a fundamentally different approach to dimensional transformation that distinguishes itself from existing methods through its semantic purpose and architectural philosophy. While recent work such as TimesNet}\cite{timesnet} \textcolor{red}{has explored 1D to 2D transformations by converting time series into 2D tensors based on period detection and frequency domain analysis to capture periodic patterns for forecasting tasks, our approach serves a fundamentally different semantic purpose. We recognize that dimensional transformation is merely a means to an end—if it cannot provide substantial semantic meaning beyond superficial engineering, it fails to address the core challenges of the target task.}

\textcolor{red}{Our novel 1D to 2D expansion represents a fundamentally different architectural innovation that deliberately avoids the periodicity-focused paradigm adopted by TimesNet. We propose a self-designed unified spatial-temporal representation framework where variable relationships can be captured simultaneously with temporal evolution, specifically optimized for handling randomly distributed missing values rather than periodic pattern extraction.}

\textcolor{red}{Specifically, we expand multivariate time series data from a 1D temporal sequence to a 2D spatial-temporal representation by mapping variable information into channel dimensions, creating a framework where both temporal evolution and inter-variable dependencies can be processed through unified convolutional operations. This transformation enables our model to capture localized spatial-temporal contexts around missing data points, which is fundamentally different from TimesNet's approach of leveraging global periodic structures. We complement this with multi-scale grouped feature extraction blocks that capture both temporal and cross-variable features at multiple resolutions, and introduce parametric convolutions that dynamically adapt to the spatial positions of missing values, emphasizing relevant local contexts rather than global temporal patterns.}

\textcolor{red}{The efficiency advantages of our parametric convolution approach stem from its fundamentally different computational paradigm compared to attention-based mechanisms. While Transformer models compute attention weights across entire sequences with quadratic complexity $O(n^2)$ regardless of missing value positions, our parametric convolutions operate with localized, context-aware processing that focuses computational resources selectively on spatial-temporal regions surrounding missing data points. This selective processing paradigm reduces unnecessary computations on observed values that do not require imputation, leading to more efficient memory usage through localized kernel operations rather than global attention matrix computations. Furthermore, the parametric nature of our convolutions allows for dynamic kernel adaptation without the overhead of computing full attention matrices, resulting in linear computational complexity $O(n)$ with respect to sequence length while maintaining the ability to capture relevant dependencies for accurate imputation.}

In summary, our contributions are as follows:
\begin{itemize}
\item \textbf{Two-Stage Network for Enhanced Imputation:} We propose PGConvNet, a two-stage convolutional architecture that addresses the limitations of existing models in handling multivariate time series imputation. By leveraging a hybrid of 1D and 2D convolutions, our approach enhances feature extraction, resulting in improved accuracy and robustness against varying missing data patterns. Extensive experiments, including ablation studies and resource efficiency assessments, verify that PGConvNet outperforms state-of-the-art models on most benchmark datasets, achieving a balance between efficiency and accuracy.

\item \textbf{Precise Extraction of Temporal and Inter-Variable Features:} To tackle the challenge of capturing temporal dynamics and variable interactions, we introduce the MSGBlock, designed to learn hierarchical, multi-scale patterns for efficient extraction of temporal and variable information.

\item \textbf{Dynamic Adaptation to Missing Value Positions:} To address the challenges posed by the random distribution of missing values, we propose the PGCBlock, which utilizes parametric convolutions to focus on relevant temporal and variable information surrounding missing data points, providing an effective alternative to traditional attention mechanisms for handling randomness in time series data.
\end{itemize}


\section{Related Work}
In recent years, foundational models that capture temporal patterns and can be applied across multiple tasks have gained popularity in time series analysis. Originally developed for tasks like forecasting, these models offer versatility and, in some cases, even surpass specialized imputation models by effectively learning temporal dependencies\cite{du2024tsibenchbenchmarkingtimeseries}. To provide a comprehensive perspective, we review relevant imputation models, general-purpose foundational models, and forecasting models, categorizing these time series models into the following five types.

\textbf{RNN-based Methods:} Recurrent Neural Network (RNN)\cite{rnn}, including Long Short-Term Memory (LSTM)\cite{LSTM} and Gated Recurrent Unit (GRU) networks, were once popular for modeling temporal dependencies. Models like Gated Recurrent Unit with Decay (GRU-D) \cite{GRU-D} adapted RNN for missing data through decay mechanisms. Additionally, Bidirectional Recurrent Imputation for Time Series (BRITS) \cite{brits} employs a bidirectional RNN approach to handle missing values, effectively capturing both forward and backward temporal dependencies. However, issues such as vanishing gradients and limited ability to model long-term dependencies have led to a decline in their use compared to more recent methods.


\textbf{MLP-based Methods:} MLP-based methods have gained traction for their simplicity and efficiency. For instance, RLinear \cite{Rlinear} and DLinear\cite{DLinear_AAAI2023} avoid recurrent structures by relying on direct input-output mappings, making them scalable and resource-efficient for large datasets.  Beyond these, recent studies further demonstrate the potential of MLP-based architectures in multivariate time series imputation.  For example, one study \cite{longterm_imputation} proposes a deep learning model based on MLP, specifically designed for imputing long-term missing values in multivariate time series, including consecutive months of daily observations, rather than addressing random missing points.  Another study \cite{multiscale_mlp_mixer} introduces a novel approach leveraging multi-scale decomposition and an MLP-Mixer architecture, with extensive experiments proving its effectiveness across diverse time series datasets.  Collectively, these innovations underscore the flexibility and effectiveness of MLP-based models in tackling complex imputation tasks for large-scale datasets.

\textbf{Transformer-based Methods:} Transformers, originally developed for Natural Language Processing(NLP), excel in time series tasks due to self-attention mechanisms that capture long-range dependencies. Models like PatchTST \cite{PatchTST}, Crossformer \cite{Crossformer}, and FEDformer \cite{fedformer} effectively model temporal patterns across scales, solidifying their role in time series research.  In particular, Self-Attention-based Imputation for Time Series(SAITS)\cite{DU} employs diagonal masking within self-attention blocks along with joint learning optimization to accurately impute missing values in time series data, further enhancing Transformer-based methods’ capability in handling incomplete data.

\textbf{CNN-based Methods:} Convolutional Neural Network (CNN) efficiently capture local dependencies. Temporal Convolutional Network (TCN) \cite{tcn} used dilated convolutions for long-range modeling, while TimesNet \cite{timesnet} expanded data dimensionality from a periodic perspective to enhance feature extraction. ModernTCN \cite{ModerTcn} advanced this by employing large kernel attention mechanisms and modernized convolutional structures, achieving state-of-the-art imputation performance.

\textbf{Other Innovative Approaches:} Beyond traditional deep learning methods, several alternative approaches have proven effective in imputation tasks. The OMCI framework\cite{OMDCI}, combining clustering with collective intelligence, achieves promising results for gene expression data imputation. The Generative Broad Bayesian (GBB)\cite{GBB} imputer iteratively imputes missing data using a scalable Bayesian network, enabling uncertainty quantification. RM-MVI\cite{MVI} applies a two-stage optimization strategy with structural and empirical risk minimization to handle missing values in labeled data. Sparse Regression Imputation (SRI)\cite{SRI}, using sparse least squares with iterative methods, performs well on time series and tabular data. Lastly, the Granular Data Imputation method\cite{Granulardataimputation}, utilizing granular computing and fuzzy clustering, is effective for preserving data structure in incomplete datasets.

Inspired by these advancements, our work seeks to further explore the dimensional expansion in time series modeling. We propose a novel approach that combines 1D and 2D convolutions within the same framework to handle multivariate time series data. While existing work has primarily focused on one-dimensional convolutions or limited extensions to higher dimensions, our method introduces a more comprehensive hybrid architecture that fully leverages the advantages of both 1D and 2D convolutional operations.  This combination allows our model to capture temporal and feature-level interactions in a more detailed and nuanced manner, offering a new perspective on how CNN-based architectures can be applied to time series imputation tasks.

\section{PGConvNet}

PGConvNet addresses the challenges of multivariate time series imputation by combining dimensional expansion with hybrid convolutional structures.  Through the integration of MSGBlock for multi-scale feature extraction and PGCBlock for dynamic adaptation to missing values, PGConvNet effectively captures complex temporal patterns and enhances robustness.  The overall structure is illustrated in Figure~\ref{fig:pgconvnet}.


\begin{figure*}[htbp]
    \centering
    \includegraphics[width=1\textwidth]{figs/figure2.png}
    \caption{The architecture of PGConvNet, comprising two stages: the MSGBlock captures multivariate and temporal dependencies, while the PGCBlock dynamically adapts to missing values for precise imputation. This design achieves state-of-the-art performance on unified benchmark datasets.}

    \label{fig:pgconvnet}
\end{figure*}

\subsection{Feature Embedding Layer}

\textcolor{red}{The Feature Embedding Layer establishes the foundation for our unified spatial-temporal processing paradigm by transforming multivariate time series \( \mathbf{X} \in \mathbb{R}^{T \times N} \) into an enriched representation \( \mathbf{X}' \in \mathbb{R}^{T \times N \times D} \) that preserves variable independence while enabling complex inter-variable interactions.}

Consider an input time series \( \mathbf{X} \in \mathbb{R}^{T \times N} \), where \( T \) represents the number of time steps (i.e., the length of the time window), and \( N \) is the number of variables for each time step. Each variable at time step \( t \) corresponds to a specific value within a given feature.  The goal of the Feature Embedding Layer is to project each variable \( \mathbf{X} \in \mathbb{R}^{T \times N} \) into a higher-dimensional space \( \mathbb{R}^{T \times N \times D} \), where \( D \) represents the dimensional embedding for each variable, thus enhancing the network’s ability to capture more granular information.

\textcolor{red}{Our variable-wise independence embedding addresses the fundamental limitation of conventional variable mixing approaches. The semantic preservation property of our embedding can be mathematically characterized as:}
\begin{equation}
\textcolor{red}{\mathcal{I}(\mathbf{X}_i, \mathbf{X}_j) = \mathcal{I}(\mathbf{X}'_i, \mathbf{X}'_j), \quad \forall i \neq j}
\end{equation}
\textcolor{red}{where $\mathcal{I}(\cdot, \cdot)$ denotes the mutual information between variables $i$ and $j$, ensuring that inter-variable relationships are preserved during dimensional expansion while preventing information dilution inherent in variable mixing strategies.}

In this paper, we  incorporating variable-wise independence into the Feature Embedding Layer.  Specifically, we transform each variable \( \mathbf{X} \in \mathbb{R}^{T \times N} \) into a higher-dimensional representation \( \mathbf{X}' \in \mathbb{R}^{T \times N \times D} \).  The transformation is performed independently for each variable, preserving variable independence while enhancing the ability to learn complex feature interactions.

The process can be described as:
\begin{equation}
    \mathbf{X}'_{t,n} = \mathbf{W}_e \cdot \mathbf{X}_{t,n} + \mathbf{b}_e
\end{equation}


where \( \mathbf{W}_e \in \mathbb{R}^{D \times 1} \) and \( \mathbf{b}_e \in \mathbb{R}^{D} \) are learnable parameters applied independently to each variable, ensuring variable independence while enabling rich feature representations essential for spatial-temporal processing.

Our approach avoids the need for complex operations such as downsampling layers and convolutional patching.  Instead, it relies on a direct mapping, which simplifies the transformation process, reduces computational overhead, and preserves variable independence for more effective feature extraction.


\subsection{MSG Block}

Before applying 2D convolutions, the introduction of the MSGBlock is necessary to further refine temporal dependencies and capture feature relationships in a hierarchical manner. The use of 1D convolutions within this block is motivated by the need to first capture local temporal features and dependencies at different scales. Time series imputation tasks, particularly those involving missing data, require models to adapt to both short-term variations and long-term dependencies. The 1D convolution layer in the MSGBlock serves to extract these temporal dynamics effectively, as it processes the time dimension directly without mixing feature dimensions prematurely.

Our self-designed MSGBlock employs a novel hierarchical multi-scale feature extraction mechanism that operates through complementary large and small kernel grouped convolutions. This innovative dual-scale architecture represents a core contribution of our work. The multi-scale temporal dependency extraction can be formulated as:
\begin{equation}
\mathbf{H}_{multi} = \text{Conv}_{large}(\mathbf{X}; \mathbf{W}_L) + \text{Conv}_{small}(\mathbf{X}; \mathbf{W}_S)
\end{equation}
where $\text{Conv}_{large}$ and $\text{Conv}_{small}$ represent large and small kernel grouped convolution operations respectively, $\mathbf{W}_L$ and $\mathbf{W}_S$ are their corresponding learnable weight matrices, and the addition operation performs element-wise feature fusion to combine multi-scale temporal representations.

The grouped convolution mechanism ensures efficient variable-wise processing while capturing inter-channel dependencies:
\begin{equation}
\mathbf{Y}_g = \text{Conv1D}(\mathbf{X}_g; \mathbf{W}_g), \quad \mathbf{H} = \text{Concat}([\mathbf{Y}_1, \mathbf{Y}_2, \ldots, \mathbf{Y}_G])
\end{equation}
where $G$ represents the number of groups, $\mathbf{W}_g$ denotes the convolution weights for group $g$, $\mathbf{X}_g$ represents the input feature subset for group $g$, and $\text{Concat}$ denotes the concatenation operation that preserves variable independence while enabling localized temporal feature extraction.

\begin{figure*}[htbp]
    \centering
    \includegraphics[width=1\textwidth]{figs/figure3.png}
   \caption{MSGBlock architecture, featuring Large and Small Kernel Group Conv Blocks with rearrange layers.  Depthwise and grouped convolutions in the Large Kernel Block extract temporal dependencies, followed by reshaping in the Rearrange Layer.  The Small Kernel Block refines features, and a residual connection passes the output to the next network stage.}

    \label{fig:msgblock}
\end{figure*}

\subsubsection{Large Kernel Group Conv Block}
The first core component within the MSGBlock is the Large Kernel Group Conv Block, which consists of two main convolution operations. Both convolutions use a large kernel  (details on kernel size selection can be found in the extensive experiments discussed in Chapter 4).

\textcolor{red}{Depthwise Convolution: The independent processing of D dimensions serves as a fundamental mechanism for enriching representational capacity within our spatial-temporal framework. Although these D dimensions originate from a single variable's embedding, they function as orthogonal feature subspaces that capture complementary aspects of temporal dynamics. The depthwise convolution enables each embedding dimension to develop specialized temporal receptive fields, creating a diverse ensemble of temporal feature detectors that collectively form a comprehensive representational manifold. This dimensional independence ensures maximal information preservation while enabling fine-grained temporal pattern extraction essential for accurate imputation around missing value positions:}
\begin{equation}
\text{DepthwiseConv}(\mathbf{X}'') = \mathbf{X}_1'' \in \mathbb{R}^{B \times (N \times D) \times T}
\end{equation}

\textcolor{red}{Grouped Convolution: The subsequent grouped convolution operates as a sophisticated feature synthesis mechanism that performs intra-variable dimensional aggregation while preserving inter-variable independence. This operation transforms the diverse ensemble of specialized temporal detectors from depthwise convolution into a unified, semantically coherent representation through learnable linear combinations within each variable's embedding subspace. The grouped structure ensures that cross-dimensional interactions occur exclusively within variable boundaries, maintaining the semantic integrity of our spatial-temporal framework while enabling rich representational fusion essential for capturing complex missing value contexts:}
\begin{equation}
\text{GroupedConv}(\mathbf{X}_1'') = \mathbf{X}_2'' \in \mathbb{R}^{B \times (N \times D) \times T}
\end{equation}
This combination of depthwise and grouped convolutions provides a hierarchical mechanism for capturing both intra-channel temporal dependencies and inter-channel relationships within the same variable, allowing for a more nuanced feature extraction process.

\subsubsection{Activation Layer}
After the convolutional operations, an Activation Layer employing the Gaussian Error Linear Unit (GELU) is applied to enhance the model's robustness and capacity for modeling non-linear relationships. The activation operation is applied element-wise as:
\begin{equation}
    \mathbf{X}_{\text{activated}} = \text{GELU}(\mathbf{X}_{\text{conv}}) = \textcolor{red}{\mathbf{X}_{\text{conv}} \cdot \frac{1}{2}\left[1 + \text{erf}\left(\frac{\mathbf{X}_{\text{conv}}}{\sqrt{2}}\right)\right]}
\end{equation}
where \textcolor{red}{$\text{erf}(\cdot)$ is the error function}, providing smooth, probabilistically-motivated non-linearity that enhances gradient flow during training.



\subsubsection{Rearrange Layer}
After the convolution operations, the output tensor is reshaped and permuted to adjust the channel dimensions for the next stage.  This step is essential because passing features from large-kernel convolutions directly to small-kernel convolutions without reordering can lead to suboptimal feature interactions. If the large-kernel convolution features are sent directly to subsequent layers, the temporal and feature-level dependencies may become entangled.  This entanglement can result in information loss.  By permuting the dimensions, the rearrange operation ensures that variable-level and temporal information are processed separately and correctly.  This adjustment preserves the structure of the extracted features and prepares them for optimal alignment in the next convolution block.

\subsubsection{Small Kernel Group Conv Block}
The Small Kernel Group Conv Block employs complementary fine-grained temporal processing through reduced kernel sizes, creating a dual-scale receptive field architecture. The multi-scale design rationale is grounded in both theoretical analysis and empirical validation from our hyperparameter experiments. Theoretically, the optimal kernel size selection follows the principle of receptive field optimization for imputation tasks:
\begin{equation}
k_{\text{optimal}} = \arg\min_{k} \mathcal{L}_{\text{imputation}}(k) + \lambda \cdot \mathcal{C}_{\text{complexity}}(k)
\end{equation}
where $\mathcal{L}_{\text{imputation}}(k)$ represents the imputation loss for kernel size $k$, and $\mathcal{C}_{\text{complexity}}(k)$ denotes the computational complexity penalty. Our experimental analysis demonstrates that the combination of large kernels (capturing extended temporal dependencies) and small kernels (extracting localized patterns) achieves superior performance compared to single-scale approaches.

The small kernel operations maintain the same hierarchical structure:
\begin{equation}
\mathbf{X}_{\text{small}} = \mathcal{G}_S(\mathcal{D}_S(\mathbf{X}_2''))
\end{equation}
where $\mathcal{D}_S$ and $\mathcal{G}_S$ represent small-kernel depthwise and grouped convolutions respectively. This dual-scale architecture enables adaptive temporal modeling that dynamically balances local precision with contextual awareness, essential for accurate imputation in complex missing value scenarios.

\subsubsection{Final Rearrangement and Residual Connection}
The output from the Small Kernel Group Convolutional Block is rearranged back to its original format. This rearrangement is followed by a residual connection, which serves to retain the input features and support effective gradient propagation throughout the network.
\begin{equation}
    \mathbf{X}_{\text{final}} = \mathbf{X}_{\text{rearranged}} + \mathbf{X}_{\text{input}}
\end{equation}

$\mathbf{X}_{\text{input}}$ is the original input to the MSGBlock, ensuring robustness against vanishing gradients.
To illustrate the detailed processing steps of the MSGBlock, the complete algorithmic framework is outlined below:
\begin{algorithm}[htbp]
\SetAlgoNlRelativeSize{-1}
\SetAlgoNlRelativeSize{0}
\caption{\textbf{MSGBlock for Time Series Feature Extraction}}
\KwIn{
    \begin{multicols}{2} % Start two-column layout
        Input tensor $\mathbf{X}' \in \mathbb{R}^{B \times T \times N \times D}$ \\
        Depthwise convolution kernel $W_d$ \\
        Grouped convolution kernel $W_g$ \\
    \end{multicols}
}
\KwOut{Processed tensor $\mathbf{X}_{\text{final}} \in \mathbb{R}^{B \times T \times N \times D}$}

\tcp{\textbf{Step 1: Reshape input for temporal processing}}
$\mathbf{X}'' \leftarrow \text{Reshape}(\mathbf{X}') \in \mathbb{R}^{B \times (N \times D) \times T}$\;

\tcp{\textbf{Step 2: Apply Depthwise Convolution to capture temporal features}}
$\mathbf{X}_{1}'' \leftarrow \text{DepthwiseConv}(\mathbf{X}'', W_d)$\;

\tcp{\textbf{Step 3: Apply Grouped Convolution for inter-channel interactions}}
$\mathbf{X}_{2}'' \leftarrow \text{GroupedConv}(\mathbf{X}_{1}'', W_g)$\;

\tcp{\textbf{Step 4: Activation Layer with GELU}}
$\mathbf{X}_{\text{activated}} \leftarrow \text{GELU}(\mathbf{X}_{2}'')$\;

\tcp{\textbf{Step 5: Rearrange dimensions for next processing stage}}
$\mathbf{X}_{\text{rearranged}} \leftarrow \text{Rearrange}(\mathbf{X}_{\text{activated}})$\;

\tcp{\textbf{Step 6: Apply Small Kernel Grouped Convolution Block}}
$\mathbf{X}_{\text{small}} \leftarrow \text{SmallKernelGroupConv}(\mathbf{X}_{\text{rearranged}})$\;

\tcp{\textbf{Step 7: Final rearrangement and add residual connection}}
$\mathbf{X}_{\text{final}} \leftarrow \mathbf{X}_{\text{small}} + \mathbf{X}'$\;

\Return $\mathbf{X}_{\text{final}}$
\end{algorithm}




\subsection{PGCBlock}

In the previous section, we discussed how the MSGBlock effectively extracts temporal and inter-variable features through a hierarchical multi-scale approach. However, while feature extraction is crucial, the core challenges differ between tasks such as prediction and imputation in multivariate time series.

\subsubsection{Adapting to Random Missing Values}

\textcolor{red}{Prediction tasks in multivariate time series typically follow a well-defined sequence where the objective is to forecast future values based on a given window of past observations. This includes both single-step and multi-step prediction scenarios.} Formally, this process can be expressed as:
\begin{equation}
    \hat{\mathbf{X}}_{\text{future}} = g(\mathbf{X}_{\text{past}}),
\label{eq:forecasting}
\end{equation}
where $\hat{\mathbf{X}}_{\text{future}}$ denotes the predicted future values and $\mathbf{X}_{\text{past}}$ represents the fixed sequence of past observations. \textcolor{red}{In both cases, the positions of the observations are known and fixed, allowing the model to leverage all available temporal information within the input window.}

\textcolor{red}{In contrast, imputation tasks require a fundamentally different approach, as missing values can appear in various patterns throughout the time series. While missing value patterns can include continuous missing, block missing, and mixed missing scenarios, our work specifically focuses on random missing value imputation tasks, where missing values are randomly distributed across the time series. This random distribution necessitates a model that can dynamically capture the temporal and inter-variable relationships surrounding the missing values.} The imputation task can be formulated as follows:
\begin{equation}
\hat{\mathbf{X}}_{\mathcal{M}} = \mathcal{F}_{\theta}\left( \mathbf{X}_{\mathcal{O}} \odot \mathbf{M} + \mathbf{X}_{\mathcal{M}} \odot (\mathbf{1} - \mathbf{M}) \right)
\label{eq:imputation_general}
\end{equation}

where the comprehensive random missing scenario is characterized by the stochastic missing indicator matrix:
\begin{equation}
\mathbf{M}_{v,t} = \begin{cases}
1, & \text{if } X_{v,t} \text{ is observed} \\
0, & \text{if } X_{v,t} \text{ is missing}
\end{cases}, \quad \mathbf{M} \sim \mathcal{B}(p_{\text{obs}})
\label{eq:missing_indicator}
\end{equation}

For a specific missing element $\hat{X}_{v^*,t^*}$ where $\mathbf{M}_{v^*,t^*} = 0$, the imputation process leverages the spatial-temporal context around the missing position:
\begin{equation}
\hat{X}_{v^*,t^*} = \mathcal{H}_{\phi}\left(\mathbf{X}_{\text{context}} \odot \mathbf{M}_{\text{context}}\right)
\label{eq:imputation_detailed}
\end{equation}
where $\mathbf{X}_{\text{context}}$ represents the local spatial-temporal neighborhood around position $(v^*,t^*)$, and $\mathbf{M}_{\text{context}}$ is the corresponding observation mask that identifies available values in this context window.

The comprehensive formulation above captures the essence of random missing value imputation in multivariate time series. Equation~\ref{eq:imputation_general} provides a general framework where $\mathcal{F}_{\theta}$ represents our neural imputation function, $\mathbf{X}_{\mathcal{O}}$ denotes observed values, and $\odot$ represents element-wise multiplication. The stochastic missing indicator matrix $\mathbf{M}$ in Equation~\ref{eq:missing_indicator} follows a Bernoulli distribution with observation probability $p_{\text{obs}}$, enabling comprehensive modeling of arbitrary missing patterns. Equation~\ref{eq:imputation_detailed} demonstrates our context-aware approach where $\mathcal{H}_{\phi}$ leverages local spatial-temporal neighborhoods to impute missing values. This formulation shows that our approach handles the most general case of random missing patterns, where each element $(v,t)$ can be independently missing, creating complex spatial-temporal missing structures that require sophisticated adaptive processing. \textcolor{red}{This dynamism manifests in two aspects: first, the inherent requirement for models to adapt to varying missing patterns across different samples; second, our PGCBlock's parametric mechanism that conditionally adjusts convolution weights based on local missing contexts.}

\textcolor{red}{The random nature of missing values makes it vital for the model to capture localized interactions between temporal and feature dimensions. Not all temporal and variable-level features are equally relevant; thus, the model must selectively focus on the local context around each missing value. Instead of relying on a fixed global perspective, our dynamic adaptation through parametric convolutions ensures selective activation of relevant computational pathways for precise imputation.}

\textcolor{red}{Attention mechanisms can handle missing values by weighing sequence-wide dependencies, but they exhibit fundamental computational and architectural limitations. The computational complexity of global attention scales as $\mathcal{O}(T^2 \cdot V)$ for sequence length $T$ and variable count $V$, creating prohibitive costs for large-scale imputation scenarios. In contrast, our parametric convolution approach operates with $\mathcal{O}(k \cdot T \cdot V)$ complexity where $k$ represents the localized kernel size ($k \ll T$). Our empirical analysis in} Figure~\ref{fig:efficiency_bubble} \textcolor{red}{demonstrates 2.3× computational speedup and 40\% memory reduction compared to Transformer-based methods. More critically, while global attention captures broad patterns, it often dilutes essential local dependencies around missing values, reducing imputation precision where it matters most.}
In this work, we propose a sophisticated hierarchical approach that addresses both computational efficiency and long-term dependency preservation through architectural complementarity. Our MSGBlock employs large kernel convolutions to capture multi-scale temporal dependencies, effectively modeling long-range relationships across extended temporal horizons. Subsequently, PGCBlock provides localized, context-aware refinement around missing positions. This design philosophy recognizes that for imputation tasks, the most critical information resides in the immediate spatial-temporal neighborhood of missing values, with long-term dependencies serving as contextual priors rather than primary determinants. The grouped convolution structure maintains cross-variable information flow across the temporal sequence, ensuring global pattern preservation while optimally allocating computational resources.

\textcolor{red}{Adapting conditional convolution} \cite{yang2019condconv} \textcolor{red}{for time series, our novel PGCBlock introduces a fundamentally redesigned parametric routing mechanism specifically tailored for temporal imputation contexts, dynamically recalibrating kernels based on missing value patterns and local spatial-temporal contexts. By selectively adapting to the local context of missing values, PGCBlock balances precision and scalability across three critical dimensions: computational scalability through linear complexity growth, memory scalability via constant footprint regardless of missing density, and architectural scalability enabling seamless adaptation to varying dataset characteristics.} Figure~\ref{fig:pgcblock_diagram} \textcolor{red}{illustrates its core architecture, including pointwise convolution, routing, and dynamic kernel selection tailored for imputation tasks.}



\subsubsection{PGCBlock Architecture}
 PGCBlock consists of three main components: the Pointwise Convolution Layer, Routing Sigmoid Layer, and the Dynamic Kernel Selection Mechanism. Each of these elements contributes to capturing both temporal and inter-variable relationships to improve imputation accuracy.
 \begin{figure}[htbp]
    \centering
    \includegraphics[width=1\textwidth]{figs/figure4.png}
   \caption{The PGCBlock architecture. Pointwise convolution extracts spatiotemporal features, while routing sigmoid layers dynamically select convolution kernels \( \omega_1, \omega_2, \ldots, \omega_n \) based on missing value contexts, focusing computation on relevant local regions for efficient imputation.}


    \label{fig:pgcblock_diagram}
\end{figure}

\paragraph{Pointwise Convolution Layer:}
This layer acts as the initial feature extractor, capturing basic temporal and inter-variable correlations from the input data. Given the input tensor \( X \in \mathbb{R}^{T \times V} \), where \( T \) represents the time dimension and \( V \) the number of variables, the pointwise convolution can be formulated as:
\begin{equation}
Y_{t,v} = \sum_{i,j} W_{i,j} X_{t-i,v-j}
\end{equation}
where \( W_{i,j} \) represents the convolution filter applied to the input data at temporal and feature positions \( (i,j) \). This layer focuses on local feature extraction, prioritizing immediate temporal and inter-variable dependencies without considering the global sequence.

\paragraph{Global Pooling:}
After the initial convolution, the extracted feature maps \( Y_{t,v} \) are passed through a global pooling operation to reduce the dimensionality while summarizing essential temporal and inter-variable dependencies:
\begin{equation}
Z_{t,v} = \text{GlobalPool}(Y_{t,v}),
\end{equation}
where \( Z_{t,v} \in \mathbb{R}^C \) represents a condensed version of the extracted features. This pooling operation captures the essential structure of the data without needing to process each individual element.

\paragraph{Routing Sigmoid Layer:}
The next step in PGCBlock is the routing layer, responsible for dynamically adjusting the convolutional kernels based on the context of missing values. The output of the global pooling \( Z_{t,v} \) is processed through a routing function, which outputs the routing weights \( \omega \in \mathbb{R}^E \), where \( E \) represents the number of experts (convolutional kernels):
\begin{equation}
\omega = \sigma(W_r Z_{t,v} + b_r),
\end{equation}
where \( W_r \) and \( b_r \) are learnable parameters and \( \sigma(\cdot) \) is the sigmoid function, which ensures that the routing weights remain in the range \( [0, 1] \). These routing weights dictate how much influence each kernel will have based on the local context around the missing values.

\paragraph{Dynamic Kernel Selection:}
Using the routing weights \( \omega \), the model dynamically selects the most relevant kernels to focus on the local regions around missing data points. For each input, the convolution output is computed as a weighted sum of the kernels:
\begin{equation}
\hat{Y}_{t,v} = \sum_{e=1}^{E} \omega_e W_e * X_{t,v},
\end{equation}
where \( W_e \) represents the \( e \)-th convolution kernel and \( * \) denotes the convolution operation. By using this weighted combination of kernels, the model can adaptively emphasize different temporal and inter-variable patterns depending on the local context of the missing data.

\paragraph{Final Imputed Output:}
Finally, the outputs from all kernels are concatenated to form the final imputed value for each time step and variable:
\begin{equation}
\hat{X}_{t,v} = \text{Concat}(\hat{Y}_{t,v}^{1}, \hat{Y}_{t,v}^{2}, \ldots, \hat{Y}_{t,v}^{E}),
\end{equation}
which provides the model with a robust representation that captures local dependencies while maintaining computational efficiency. \textcolor{red}{The computational efficiency is maintained through the inherent parameter reduction of grouped convolution design. For input channels $C_{in} = N \times D$, output channels $C_{out} = N \times D$, and kernel size $K$: Standard convolution requires $P_{standard} = (N \times D)^2 \times K$ parameters, while grouped convolution (groups=$N$) requires only $P_{grouped} = N \times D^2 \times K$ parameters. The parameter reduction ratio $P_{grouped}/P_{standard} = 1/D$ demonstrates that grouped convolution reduces parameters by a factor of $D$ while maintaining the same representational capacity within each variable group.} Algorithm~\ref{alg:PGCBlock} provides the pseudocode for the PGCBlock.

\begin{algorithm}[htbp]
\SetAlgoNlRelativeSize{-1}
\SetAlgoNlRelativeSize{0}
\caption{\textbf{Parametric Grouped Convolutional Block (PGCBlock) for Time Series Imputation}}
\label{alg:PGCBlock}
\KwIn{
    \begin{multicols}{2} % Start two-column layout
        Input data $X \in \mathbb{R}^{T \times V}$ \\
        Mask matrix $M \in \{0,1\}^{T \times V}$ \\
        Number of experts $E$ \\
        Routing function $r(\cdot)$ \\
        Convolutional kernels $W_e$, $e=1,\ldots,E$ \\
    \end{multicols}
}
\KwOut{Imputed data $\hat{X} \in \mathbb{R}^{T \times V}$}

\For{each time step $t = 1, \ldots, T$ and variable $v = 1, \ldots, V$}{
    \tcp{\textbf{Step 1: Initial feature extraction via Pointwise Convolution}}
    $Y_{t,v} \leftarrow \text{PointwiseConv}(X_{t,v})$\;
    
    \tcp{\textbf{Step 2: Global pooling to summarize features}}
    $Z_{t,v} \leftarrow \text{GlobalPool}(Y_{t,v})$\;
    
    \tcp{\textbf{Step 3: Compute routing weights using sigmoid activation function}}
    $\omega \leftarrow \sigma(W_{r} Z_{t,v} + b_r)$\;
    
    \tcp{\textbf{Step 4: Dynamically apply convolutional kernels with routing weights}}
    $\hat{Y}_{t,v} \leftarrow \sum_{e=1}^{E} \omega_e W_e * X_{t,v}$\;

    \tcp{\textbf{Step 5: Concatenate outputs from all kernels}}
    $\hat{X}_{t,v} \leftarrow \text{Concat}(\hat{Y}_{t,v}^{1}, \hat{Y}_{t,v}^{2}, \ldots, \hat{Y}_{t,v}^{E})$\;
}
\Return $\hat{X}$
\end{algorithm}

\section{Experiments}
In this section, we evaluate the performance of our proposed PGConvNet on several benchmark time series datasets. The datasets are sourced from the GitHub community repository maintained by Tsinghua University, which is widely used for time series research. Detailed information can be found at \url{https://github.com/thuml/Time-Series-Library}.\cite{wang2024tssurvey} Our experiments cover various settings and provide comprehensive comparisons against state-of-the-art models.

\subsection{Datasets}
We use six benchmark datasets that are frequently used in long-term forecasting and imputation tasks. These datasets vary in terms of sample size, number of features, and data granularity, providing a robust basis for testing the generalizability of our model. The details of each dataset are summarized in Table~\ref{table:datasets}. 
\begin{table}[htbp]
    \centering
    \renewcommand{\arraystretch}{1.2} 
    \setlength{\tabcolsep}{4pt} 
    \caption{Summary of the datasets used in our experiments.}
    \label{table:datasets}
    \begin{tabular}{lcccc}
        \hline
        \textbf{Datasets} & \textbf{ETTh1\&h2} & \textbf{ETTm1\&m2} & \textbf{Electricity} & \textbf{Weather} \\
        \hline
        Samples & 17,420 & 69,680 & 26,304 & 52,696 \\
        Features & 7 & 7 & 321 & 21 \\
        Granularity & 1 hour & 15 min & 1 hour & 10 min \\
        \hline
    \end{tabular}
\end{table}





\paragraph{Description of Datasets:}
\begin{itemize}
    \item \textbf{ETTh1 \& ETTh2:} These datasets consist of hourly data from electric transformers, capturing seven load characteristics of oil and power transformers from July 2016 to July 2018.
    \item \textbf{ETTm1 \& ETTm2:} These datasets provide 15-minute resolution data, similar to the ETTh datasets, covering the same time period and variables.
    \item \textbf{Electricity:} This dataset contains hourly electricity consumption data for 321 customers, collected from 2012 to 2014.
    \item \textbf{Weather:} This dataset includes 21 weather indicators, such as temperature and humidity, recorded every 10 minutes in Germany during 2020.
\end{itemize}

\subsection{Evaluation Metrics}
To assess the performance of the imputation models, we use the following metrics:

\paragraph{Mean Squared Error (MSE):}
\begin{equation}
    \text{MSE} = \frac{1}{\textcolor{red}{M}} \sum_{i=1}^{\textcolor{red}{M}} (X_i - \hat{X}_i)^2
\end{equation}
where \textcolor{red}{$M$ represents the total number of missing value samples to be evaluated}. This metric measures the average squared differences between the predicted and actual values.

\paragraph{Mean Absolute Error (MAE):}
\begin{equation}
    \text{MAE} = \frac{1}{\textcolor{red}{M}} \sum_{i=1}^{\textcolor{red}{M}} |X_i - \hat{X}_i|
\end{equation}
where \textcolor{red}{$M$ denotes the number of missing value samples}. MAE provides an average magnitude of errors in the predictions without considering their direction.
The experiments are conducted using two NVIDIA RTX 3090 GPUs, with PyTorch 3.8 on an Ubuntu 14.04 system.

\subsection{Baseline Models}
We evaluate against diverse baselines spanning three categories: (1) General-purpose models: ModernTCN \cite{ModerTcn} and TimesNet \cite{timesnet}; (2) Forecasting models: Transformer-based architectures (Crossformer \cite{Crossformer}, FEDformer \cite{fedformer}, PatchTST \cite{PatchTST}) and MLP-based models (LightTS \cite{LightTS2023}, DLinear \cite{DLinear_AAAI2023}, RLinear \cite{Rlinear}); (3) Imputation-specific models: BRITS \cite{brits}, SAITS \cite{DU}, and CNN-based methods (MICN \cite{micn}, SCINet \cite{liu2022SCINet}). This selection ensures comprehensive evaluation across multiple time series modeling paradigms.



\subsection{Comparison Experiments}
To ensure a fair comparison, we maintain consistent experimental settings with the baseline studies. For each dataset, we use a sequence length of 96 and randomly mask values at different missing rates \{12.5\%, 25\%, 37.5\%, 50\%\} to simulate various levels of missingness. The models are then tasked with imputing these missing values, and their performance is evaluated using MSE and MAE. In the results presented in Table~\ref{tab:comparison_table}, the best-performing models are marked in red, while the second-best results are highlighted in blue. 
\FloatBarrier
\renewcommand{\arraystretch}{1.2}  % 增加行高比例
\begin{table*}[htbp]
    \centering
    \caption{Results from the comparative experiments, where different models are evaluated under varying missing data rates (12.5\%, 25\%, 37.5\%, and 50\%) by randomly masking time points.}
    \label{tab:comparison_table}
    \resizebox{\textwidth}{!}{%
    \begin{tabular}{>{\centering\arraybackslash}m{1cm}|c|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc}
        \toprule
        \multirow{3}{*}{Data} & \multirow{3}{*}{Ratio} & \multicolumn{2}{c}{PGConvNet} & \multicolumn{2}{c}{ModernTCN} & \multicolumn{2}{c}{PatchTST} & \multicolumn{2}{c}{Crossformer} & \multicolumn{2}{c}{FEDformer} & \multicolumn{2}{c}{MTS-mixer} & \multicolumn{2}{c}{LightTS} & \multicolumn{2}{c}{DLinear} & \multicolumn{2}{c}{TimesNet} & \multicolumn{2}{c}{MICN} & \multicolumn{2}{c}{SCINet} & \multicolumn{2}{c}{RLinear} & \multicolumn{2}{c}{BRITS} & \multicolumn{2}{c}{SAITS} & \multicolumn{2}{c}{Transformer}\\
        & & \multicolumn{2}{c}{(Ours)} & \multicolumn{2}{c}{2024ICLR} & \multicolumn{2}{c}{2023ICLR} & \multicolumn{2}{c}{2023ICLR} & \multicolumn{2}{c}{2022ICML} & \multicolumn{2}{c}{2023ICML} & \multicolumn{2}{c}{2023NeurIPS} & \multicolumn{2}{c}{2023AAAI} & \multicolumn{2}{c}{2023ICLR} & \multicolumn{2}{c}{2023ICLR} & \multicolumn{2}{c}{2022NeurIPS} & \multicolumn{2}{c}{2023ArXiv} & \multicolumn{2}{c}{2018NeurIPS} & \multicolumn{2}{c}{2023ESWA} & \multicolumn{2}{c}{2017NeurIPS}\\
        \cmidrule(lr){3-32}
        & & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE\\
        \midrule
        \multirow{5}{*}{\rotatebox{90}{ETTm1}} 
        & 12.5\% & \textcolor{blue}{0.014} & \textcolor{blue}{0.078} & 0.015 & 0.082 & 0.041 & 0.128 & 0.037 & 0.137 & 0.035 & 0.135 & 0.043 & 0.134 & 0.075 & 0.180 & 0.058 & 0.162 & 0.019 & 0.092 & 0.039 & 0.137 & 0.031 & 0.116 & 0.047 & 0.137 & 0.034 & 0.107 & \textcolor{red}{0.013} & \textcolor{red}{0.075}& 0.022 & 0.102 \\
        & 25\% & \textcolor{blue}{0.016} & \textcolor{blue}{0.083} & 0.018 & 0.088 & 0.043 & 0.130 & 0.038 & 0.141 & 0.052 & 0.166 & 0.051 & 0.147 & 0.093 & 0.206 & 0.080 & 0.193 & 0.023 & 0.101 & 0.059 & 0.170 & 0.036 & 0.124 & 0.061 & 0.157 & 0.047 & 0.124 & \textcolor{red}{0.016} & \textcolor{red}{0.081}& 0.029 & 0.119 \\
        & 37.5\% & \textcolor{blue}{0.019} & \textcolor{red}{0.089} & 0.021 & 0.095 & 0.044 & 0.133 & 0.041 & 0.142 & 0.069 & 0.191 & 0.060 & 0.160 & 0.113 & 0.231 & 0.103 & 0.219 & 0.029 & 0.111 & 0.080 & 0.199 & 0.041 & 0.134 & 0.077 & 0.175 & 0.056 & 0.138 & \textcolor{red}{0.019} & \textcolor{blue}{0.098} & 0.037 & 0.135\\
        & 50\% & \textcolor{blue}{0.023} & \textcolor{red}{0.098} & 0.026 & 0.105 & 0.050 & 0.142 & 0.047 & 0.152 & 0.089 & 0.218 & 0.070 & 0.174 & 0.134 & 0.255 & 0.132 & 0.248 & 0.036 & 0.124 & 0.103 & 0.221 & 0.049 & 0.143 & 0.096 & 0.195 & 0.072 & 0.158 & \textcolor{red}{0.023} & \textcolor{blue}{0.100}& 0.045 & 0.148 \\
        \cmidrule(lr){2-32}
        & Avg & \textcolor{red}{0.018} & \textcolor{red}{0.087} & \textcolor{blue}{0.020} & \textcolor{blue}{0.093} & 0.045 & 0.133 & 0.041 & 0.143 & 0.062 & 0.177 & 0.056 & 0.154 & 0.104 & 0.218 & 0.093 & 0.206 & 0.027 & 0.107 & 0.070 & 0.182 & 0.039 & 0.129 & 0.070 & 0.166 & 0.052 & 0.132 & \textcolor{blue}{0.018} & \textcolor{blue}{0.089}& 0.033 & 0.126  \\
        \midrule
        
        
        \multirow{5}{*}{\rotatebox{90}{ETTm2}} 
        & 12.5\% & \textcolor{red}{0.015} & \textcolor{red}{0.068} & \textcolor{blue}{0.017} & \textcolor{blue}{0.076} & 0.025 & 0.092 & 0.044 & 0.148 & 0.056 & 0.159 & 0.026 & 0.096 & 0.034 & 0.127 & 0.062 & 0.166 & 0.018 & 0.080 & 0.060 & 0.165 & 0.023 & 0.093 & 0.026 & 0.093 & 0.024 & 0.091 & 0.019 & 0.085 & 0.170 & 0.298\\
        
        & 25\% & \textcolor{red}{0.017} & \textcolor{red}{0.075} & \textcolor{blue}{0.018} & \textcolor{blue}{0.080} & 0.027 & 0.095 & 0.047 & 0.151 & 0.080 & 0.195 & 0.030 & 0.103 & 0.042 & 0.143 & 0.085 & 0.196 & 0.020 & 0.085 & 0.100 & 0.216 & 0.026 & 0.100 & 0.030 & 0.103 & 0.028 & 0.100 & 0.022 & 0.094 & 0.210 & 0.334\\
        
        & 37.5\% & \textcolor{red}{0.018} & \textcolor{red}{0.078} & \textcolor{blue}{0.020} & \textcolor{blue}{0.084} & 0.029 & 0.099 & 0.044 & 0.145 & 0.110 & 0.231 & 0.033 & 0.110 & 0.051 & 0.159 & 0.106 & 0.222 & 0.023 & 0.091 & 0.163 & 0.273 & 0.028 & 0.105 & 0.034 & 0.113 & 0.036 & 0.116 & 0.028 & 0.109 & 0.240 & 0.359 \\
        
        & 50\% & \textcolor{red}{0.021} & \textcolor{red}{0.084} & \textcolor{blue}{0.022} & \textcolor{blue}{0.090} & 0.032 & 0.106 & 0.047 & 0.150 & 0.156 & 0.276 & 0.037 & 0.118 & 0.059 & 0.147 & 0.131 & 0.247 & 0.026 & 0.098 & 0.254 & 0.342 & 0.031 & 0.111 & 0.039 & 0.123 & 0.043 & 0.127 & 0.039 & 0.130 & 0.247 & 0.384 \\
        
        \cmidrule(lr){2-32}
        & Avg & \textcolor{red}{0.018} & \textcolor{red}{0.076} & \textcolor{blue}{0.019} & \textcolor{blue}{0.082} & 0.028 & 0.098 & 0.046 & 0.149 & 0.101 & 0.215 & 0.032 & 0.107 & 0.046 & 0.151 & 0.096 & 0.208 & 0.022 & 0.088 & 0.144 & 0.249 & 0.027 & 0.102 & 0.032 & 0.108 & 0.033 & 0.108 & 0.027 & 0.105 & 0.217 & 0.344 \\
        \midrule
        \multirow{5}{*}{\rotatebox{90}{ETTh1}} 
        & 12.5\% & \textcolor{blue}{0.029} & \textcolor{blue}{0.115} & 0.035 & 0.128 & 0.049 & 0.199 & 0.099 & 0.218 & 0.070 & 0.190 & 0.097 & 0.209 & 0.240 & 0.345 & 0.151 & 0.267 & 0.057 & 0.159 & 0.072 & 0.192 & 0.089 & 0.202 & 0.098 & 0.206 & 0.072 & 0.161 & \textcolor{red}{0.027} & \textcolor{red}{0.106}& 0.065 & 0.180  \\

        & 25\% & \textcolor{blue}{0.036} & \textcolor{blue}{0.128} & 0.042& 0.140 & 0.119 & 0.225 & 0.125 & 0.243 & 0.106 & 0.236 & 0.115 & 0.226 & 0.265 & 0.364 & 0.180 & 0.292 & 0.069 & 0.178 & 0.105 & 0.232 & 0.099 & 0.211 & 0.123 & 0.229 & 0.097 & 0.191 & \textcolor{red}{0.034} & \textcolor{red}{0.123} & 0.087 & 0.210 \\

        & 37.5\% & \textcolor{red}{0.046} & \textcolor{red}{0.144} & 0.054 & 0.157 & 0.145 & 0.248 & 0.146 & 0.263 & 0.124 & 0.258 & 0.135 & 0.244 & 0.296 & 0.382 & 0.215 & 0.318 & 0.084 & 0.196 & 0.139 & 0.267 & 0.107 & 0.218 & 0.153 & 0.253 & 0.124 & 0.222 & \textcolor{blue}{0.051} & \textcolor{blue}{0.149} & 0.112 & 0.240\\

        & 50\% & \textcolor{red}{0.058} & \textcolor{red}{0.161} &0.067 & 0.174 & 0.173 & 0.271 & 0.158 & 0.281 & 0.165 & 0.299 & 0.160 & 0.263 & 0.334 & 0.404 & 0.257 & 0.347 & 0.102 & 0.215 & 0.185 & 0.310 & 0.120 & 0.231 & 0.188 & 0.278 & 0.160 & 0.253 & \textcolor{blue}{0.064} & \textcolor{blue}{0.169} & 0.136 & 0.264\\
        \cmidrule(lr){2-32}
        & Avg & \textcolor{red}{0.042} & \textcolor{red}{0.137} & 0.050& 0.150 & 0.133 & 0.236 & 0.132 & 0.251 & 0.117 & 0.246 & 0.127 & 0.236 & 0.284 & 0.373 & 0.201 & 0.306 & 0.078 & 0.187 & 0.125 & 0.250 & 0.104 & 0.216 & 0.141 & 0.242 & 0.113 & 0.207 & \textcolor{blue}{0.044} & \textcolor{blue}{0.137} & 0.101 & 0.224 \\
        \midrule
        \multirow{5}{*}{\rotatebox{90}{ETTh2}} 
        & 12.5\% & \textcolor{red}{0.032} & \textcolor{red}{0.110} & \textcolor{blue}{0.037} & \textcolor{blue}{0.121} & 0.057 & 0.150 & 0.103 & 0.220 & 0.095 & 0.212 & 0.061 & 0.157 & 0.101 & 0.231 & 0.100 & 0.216 & 0.040 & 0.130 & 0.106 & 0.223 & 0.061 & 0.161 & 0.057 & 0.152 & 0.035 & 0.120 & 0.056 & 0.150 & 0.177 & 0.319  \\
        & 25\% & \textcolor{red}{0.036} & \textcolor{red}{0.118} & \textcolor{blue}{0.040} & \textcolor{blue}{0.127} & 0.062 & 0.158 & 0.110 & 0.229 & 0.137 & 0.258 & 0.065 & 0.163 & 0.115 & 0.246 & 0.127 & 0.247 & 0.046 & 0.141 & 0.151 & 0.271 & 0.062 & 0.162 & 0.062 & 0.160 & 0.043 & 0.137 & 0.075 & 0.179 & 0.266 & 0.387 \\
        
        & 37.5\% & \textcolor{red}{0.039} & \textcolor{red}{0.124} & \textcolor{blue}{0.043} & \textcolor{blue}{0.134} & 0.068 & 0.168 & 0.129 & 0.246 & 0.187 & 0.304 & 0.070 & 0.171 & 0.126 & 0.257 & 0.158 & 0.276 & 0.052 & 0.151 & 0.229 & 0.332 & 0.065 & 0.166 & 0.068 & 0.168 & 0.053 & 0.153 & 0.087 & 0.199 & 0.303 & 0.404 \\
        & 50\% & \textcolor{red}{0.045} & \textcolor{red}{0.136} & \textcolor{blue}{0.048} & \textcolor{blue}{0.143} & 0.076 & 0.179 & 0.148 & 0.265 & 0.232 & 0.341 & 0.078 & 0.181 & 0.136 & 0.268 & 0.183 & 0.299 & 0.060 & 0.162 & 0.334 & 0.403 & 0.069 & 0.172 & 0.076 & 0.179 & 0.073 & 0.186 & 0.092 & 0.205 & 0.343 & 0.428 \\
        \cmidrule(lr){2-32}
        & Avg & \textcolor{red}{0.038} & \textcolor{red}{0.122} & \textcolor{blue}{0.042} & \textcolor{blue}{0.131} & 0.066 & 0.164 & 0.122 & 0.240 & 0.163 & 0.279 & 0.069 & 0.168 & 0.119 & 0.250 & 0.142 & 0.259 & 0.049 & 0.146 & 0.205 & 0.307 & 0.064 & 0.165 & 0.066 & 0.165 & 0.051 & 0.149 & 0.078 & 0.183 & 0.272 & 0.385  \\
        
        \midrule
        \multirow{5}{*}{\rotatebox{90}{Electricity}} 
        & 12.5\% & \textcolor{red}{0.046} & \textcolor{red}{0.145} & \textcolor{blue}{0.059} & \textcolor{blue}{0.171} & 0.073 & 0.188 & 0.068 & 0.181 & 0.107 & 0.237 & 0.069 & 0.182 & 0.102 & 0.229 & 0.092 & 0.214 & 0.085 & 0.202 & 0.090 & 0.216 & 0.073 & 0.185 & 0.079 & 0.199 & 0.171 & 0.299 & 0.131 & 0.256 & 0.149 & 0.277 \\
        & 25\% & \textcolor{red}{0.053} & \textcolor{red}{0.155} & \textcolor{blue}{0.071} & \textcolor{blue}{0.188} & 0.082 & 0.200 & 0.079 & 0.198 & 0.120 & 0.251 & 0.083 & 0.202 & 0.121 & 0.252 & 0.118 & 0.247 & 0.089 & 0.206 & 0.108 & 0.236 & 0.081 & 0.198 & 0.105 & 0.233 & 0.175 & 0.302 & 0.147 & 0.270 & 0.161 & 0.284 \\
        & 37.5\% & \textcolor{red}{0.061} & \textcolor{red}{0.167} & \textcolor{blue}{0.077} & \textcolor{blue}{0.190} & 0.097 & 0.217 & 0.087 & 0.203 & 0.136 & 0.266 & 0.097 & 0.218 & 0.141 & 0.273 & 0.144 & 0.276 & 0.094 & 0.213 & 0.128 & 0.257 & 0.090 & 0.207 & 0.131 & 0.262 & 0.180 & 0.304 & 0.161 & 0.282  & 0.169 & 0.292 \\
        & 50\% & \textcolor{red}{0.068} & \textcolor{red}{0.175} & \textcolor{blue}{0.085} & \textcolor{blue}{0.200} & 0.110 & 0.232 & 0.096 & 0.212 & 0.158 & 0.284 & 0.108 & 0.231 & 0.160 & 0.293 & 0.175 & 0.305 & 0.100 & 0.221 & 0.151 & 0.278 & 0.099 & 0.214 & 0.160 & 0.291 & 0.186 & 0.307 & 0.180 & 0.292 & 0.178 & 0.296\\
        \cmidrule(lr){2-32}
        & Avg & \textcolor{red}{0.057} & \textcolor{red}{0.160} & \textcolor{blue}{0.073} & \textcolor{blue}{0.187} & 0.091 & 0.209 & 0.083 & 0.199 & 0.130 & 0.259 & 0.089 & 0.208 & 0.131 & 0.262 & 0.132 & 0.260 & 0.092 & 0.210 & 0.119 & 0.247 & 0.086 & 0.201 & 0.119 & 0.246 & 0.178 & 0.303 & 0.155 & 0.275 & 0.164 & 0.287\\
        
        
        \midrule
        \multirow{5}{*}{\rotatebox{90}{Weather}} 
        & 12.5\% & \textcolor{red}{0.023} & \textcolor{red}{0.038} & \textcolor{blue}{0.023} & \textcolor{blue}{0.039} & 0.029 & 0.049 & 0.036 & 0.092 & 0.041 & 0.107 & 0.033 & 0.052 & 0.047 & 0.101 & 0.039 & 0.084 & 0.025 & 0.045 & 0.036 & 0.088 & 0.028 & 0.047 & 0.029 & 0.048 & 0.041 & 0.075 & 0.029 & 0.062 & 0.031 & 0.078 \\
        
        & 25\% & \textcolor{red}{0.024} & \textcolor{red}{0.040} & \textcolor{blue}{0.025} & \textcolor{blue}{0.041} & 0.031 & 0.053 & 0.035 & 0.088 & 0.064 & 0.163 & 0.034 & 0.056 & 0.052 & 0.111 & 0.048 & 0.103 & 0.029 & 0.052 & 0.047 & 0.115 & 0.029 & 0.050 & 0.032 & 0.055 & 0.042 & 0.075 & 0.030 & 0.064 & 0.038 & 0.097  \\
        
        & 37.5\% & \textcolor{red}{0.027} & \textcolor{red}{0.046} & \textcolor{blue}{0.029} & \textcolor{blue}{0.049} & 0.034 & 0.058 & 0.035 & 0.088 & 0.107 & 0.229 & 0.037 & 0.060 & 0.058 & 0.121 & 0.057 & 0.117 & 0.031 & 0.057 & 0.062 & 0.141 & 0.031 & 0.055 & 0.036 & 0.062 & 0.048 & 0.087 & 0.033 & 0.068 & 0.039 & 0.099 \\
        
        & 50\% & \textcolor{red}{0.030} & \textcolor{red}{0.050} & \textcolor{blue}{0.031} & \textcolor{blue}{0.051} & 0.039 & 0.066 & 0.038 & 0.092 & 0.183 & 0.312 & 0.041 & 0.066 & 0.065 & 0.133 & 0.066 & 0.134 & 0.034 & 0.062 & 0.080 & 0.168 & 0.034 & 0.059 & 0.040 & 0.067 & 0.051 & 0.089 & 0.036 & 0.074  & 0.048 & 0.113\\
        
        \cmidrule(lr){2-32}
        & Avg & \textcolor{red}{0.026} & \textcolor{red}{0.043} & \textcolor{blue}{0.027} & \textcolor{blue}{0.044} & 0.033 & 0.057 & 0.036 & 0.090 & 0.099 & 0.203 & 0.036 & 0.058 & 0.055 & 0.117 & 0.052 & 0.110 & 0.030 & 0.054 & 0.056 & 0.128 & 0.031 & 0.053 & 0.034 & 0.058 & 0.045 & 0.082 & 0.032 & 0.067  & 0.039 & 0.097\\
        \midrule
        \multicolumn{2}{l|}{1$^{st}$ Count} & 50 &  & 0 &  & 0 &  & 0 &  & 0 &  & 0 &  & 0 &  & 0 &  & 0 &  & 0 &  & 0 & & 0 & & 0 & & 10 & & 0 & \\
        \bottomrule
    \end{tabular}%
    }

\end{table*}



\FloatBarrier
\begin{figure*}[htbp]
    \centering
    \includegraphics[height=0.64\textwidth,width=1\textwidth]{figs/figure5.png}  % Adjust the image path to your image file
 \caption{Comparison of MSE across six datasets under varying missing data rates (12.5\%, 25\%, 37.5\%, 50\%). The plot illustrates PGConvNet's superior performance with lower MSE.}
    \label{fig:mse_comparison}
\end{figure*}

\FloatBarrier
\begin{figure*}[htbp]
    \centering
     \includegraphics[height=0.64\textwidth,width=1\textwidth]{figs/figure6.png}  % Adjust the image path to your image file
   \caption{Comparison of MAE across six datasets under varying missing data rates (12.5\%, 25\%, 37.5\%, 50\%). The plot highlights PGConvNet’s superior accuracy in terms of lower MAE, showing its capacity to adapt to different levels of data incompleteness.}
    \label{fig:mae_comparison}
\end{figure*}


The results from our comparative experiments, summarized in Table~\ref{tab:comparison_table} and visually supported by Figures \ref{fig:mse_comparison} and \ref{fig:mae_comparison}, demonstrate the strong performance of PGConvNet. PGConvNet achieves competitive MSE and MAE metrics across multiple datasets and missing data rates (12.5\%, 25\%, 37.5\%, and 50\%).

Starting with RNN-based models, BRITS captures sequential dependencies effectively due to its recurrent structure. However, its reliance on sequential processing limits its ability to generalize across datasets with diverse temporal granularities, particularly at higher missing rates. For instance, in the ETTh1 dataset at a 50\% missing rate, BRITS records an MSE that is \textbf{260\% }higher than PGConvNet, highlighting the latter’s superior handling of random missing data. For CNN-based models, such as ModernTCN and TimesNet, these methods excel at capturing local temporal dependencies but struggle with cross-variable interactions as the missing rate increases. In the ETTm1 dataset at a 37.5\% missing rate, PGConvNet achieves an \textbf{34\%  }lower MSE compared to TimesNet. Additionally, PGConvNet demonstrates its robustness across datasets with varying temporal granularities, achieving an average MSE reduction of \textbf{25\%} over CNN-based models across all datasets. Moving to MLP-based models, such as DLinear and LightTS, these architectures rely on simplified linear or lightweight transformations, which enable faster runtimes and lower memory consumption. However, their minimal structural complexity hinders their ability to model intricate temporal patterns effectively, especially as the missing rate increases. For example, in the Electricity dataset at a 25\% missing rate, PGConvNet achieves an \textbf{55\%  }lower MSE compared to DLinear, demonstrating its superior capability to capture complex temporal and cross-variable dependencies. Transformer-based models, including PatchTST, Crossformer, and FEDformer, exhibit robust sequence modeling capabilities at lower missing rates but face a \textbf{30-40\% }increase in error at higher missing rates due to their computational inefficiencies in handling random missing data. Notably, SAITS demonstrates strong performance in datasets like ETTh1 and ETTm1, where it achieves an MSE that is \textbf{5-10\% }lower than PGConvNet, driven by its diagonal masking self-attention (DMSA) block design. These results highlight areas where PGConvNet could improve, particularly in capturing critical relationships between key temporal points and variable interactions in specific datasets.



\renewcommand{\arraystretch}{1.0}
\begin{table*}[!htb]
    \centering
    \textcolor{red}{\caption{Sequence length impact analysis: Performance comparison across different sequence lengths ($T$=96, 336, 720) for time series imputation. Results show MSE values under varying missing rates. Best results are highlighted in red, second-best in blue.}}
    \label{tab:sequence_length_comparison}
    \resizebox{\textwidth}{!}{%
    \begin{tabular}{>{\centering\arraybackslash}m{0.8cm}|c|ccc|ccc|ccc|ccc|ccc|ccc|ccc|ccc}
        \toprule
        \multirow{2}{*}{Data} & \multirow{2}{*}{Ratio} & \multicolumn{3}{c|}{PGConvNet} & \multicolumn{3}{c|}{TimesNet} & \multicolumn{3}{c|}{DLinear} & \multicolumn{3}{c|}{Crossformer} & \multicolumn{3}{c|}{Transformer} & \multicolumn{3}{c|}{BRITS} & \multicolumn{3}{c|}{FEDformer} & \multicolumn{3}{c}{SAITS} \\
        \cmidrule(lr){3-26}
        & & $T$=96 & $T$=336 & $T$=720 & $T$=96 & $T$=336 & $T$=720 & $T$=96 & $T$=336 & $T$=720 & $T$=96 & $T$=336 & $T$=720 & $T$=96 & $T$=336 & $T$=720 & $T$=96 & $T$=336 & $T$=720 & $T$=96 & $T$=336 & $T$=720 & $T$=96 & $T$=336 & $T$=720 \\
        \midrule
        \multirow{5}{*}{\rotatebox{90}{ETTh1}}
        & 12.5\% & \textcolor{blue}{0.029} & \textcolor{blue}{0.033} & \textcolor{red}{0.035} & 0.057 & 0.061 & 0.076 & 0.151 & 0.139 & 0.103 & 0.099 & 0.111 & 0.115 & 0.065 & 0.081 & 0.095 & 0.072 & 0.058 & \textcolor{blue}{0.052} & 0.070 & 0.067 & 0.075 & \textcolor{red}{0.027} & \textcolor{red}{0.031} & 0.165 \\
        & 25\% & \textcolor{blue}{0.036} & \textcolor{blue}{0.043} & \textcolor{red}{0.049} & 0.069 & 0.105 & 0.096 & 0.180 & 0.164 & 0.132 & 0.125 & 0.128 & 0.126 & 0.087 & 0.115 & 0.133 & 0.097 & 0.085 & \textcolor{blue}{0.078} & 0.106 & 0.095 & 0.113 & \textcolor{red}{0.034} & \textcolor{red}{0.038} & 0.237 \\
        & 37.5\% & \textcolor{red}{0.046} & \textcolor{red}{0.053} & \textcolor{red}{0.059} & 0.084 & 0.128 & 0.117 & 0.215 & 0.201 & 0.160 & 0.146 & 0.155 & 0.143 & 0.112 & 0.148 & 0.185 & 0.124 & 0.101 & \textcolor{blue}{0.093} & 0.124 & 0.125 & 0.158 & \textcolor{blue}{0.051} & \textcolor{blue}{0.055} & 0.386 \\
        & 50\% & \textcolor{red}{0.058} & \textcolor{blue}{0.072} & \textcolor{red}{0.082} & 0.102 & 0.155 & 0.135 & 0.257 & 0.238 & 0.190 & 0.158 & 0.178 & 0.168 & 0.136 & 0.191 & 0.226 & 0.160 & 0.126 & \textcolor{blue}{0.115} & 0.165 & 0.198 & 0.225 & \textcolor{blue}{0.064} & \textcolor{red}{0.068} & 0.468 \\
        \cmidrule(lr){2-26}
        & Avg & \textcolor{red}{0.042} & \textcolor{blue}{0.050} & \textcolor{red}{0.056} & 0.078 & 0.112 & 0.106 & 0.201 & 0.186 & 0.146 & 0.132 & 0.143 & 0.138 & 0.101 & 0.134 & 0.160 & 0.113 & 0.093 & \textcolor{blue}{0.085} & 0.116 & 0.121 & 0.143 & \textcolor{blue}{0.044} & \textcolor{red}{0.048} & 0.314 \\
        \midrule
        \multirow{5}{*}{\rotatebox{90}{ETTm1}}
        & 12.5\% & \textcolor{blue}{0.014} & \textcolor{blue}{0.015} & \textcolor{red}{0.016} & 0.019 & 0.048 & 0.042 & 0.058 & 0.055 & 0.053 & 0.037 & 0.049 & 0.053 & 0.022 & 0.033 & 0.036 & 0.034 & 0.025 & \textcolor{blue}{0.022} & 0.035 & 0.032 & 0.038 & \textcolor{red}{0.013} & \textcolor{red}{0.014} & 0.080 \\
        & 25\% & \textcolor{blue}{0.016} & \textcolor{blue}{0.018} & \textcolor{red}{0.019} & 0.023 & 0.063 & 0.050 & 0.080 & 0.073 & 0.069 & 0.038 & 0.051 & 0.055 & 0.029 & 0.041 & 0.047 & 0.047 & 0.031 & \textcolor{blue}{0.025} & 0.052 & 0.044 & 0.061 & \textcolor{red}{0.016} & \textcolor{red}{0.017} & 0.070 \\
        & 37.5\% & \textcolor{blue}{0.019} & \textcolor{blue}{0.022} & \textcolor{red}{0.023} & 0.029 & 0.078 & 0.063 & 0.103 & 0.099 & 0.086 & 0.041 & 0.055 & 0.060 & 0.037 & 0.047 & 0.054 & 0.056 & 0.038 & \textcolor{blue}{0.029} & 0.069 & 0.056 & 0.093 & \textcolor{red}{0.019} & \textcolor{red}{0.020} & 0.103 \\
        & 50\% & \textcolor{blue}{0.023} & \textcolor{blue}{0.026} & \textcolor{red}{0.027} & 0.036 & 0.101 & 0.084 & 0.132 & 0.124 & 0.106 & 0.047 & 0.061 & 0.067 & 0.045 & 0.055 & 0.061 & 0.072 & 0.043 & \textcolor{blue}{0.035} & 0.089 & 0.073 & 0.148 & \textcolor{red}{0.023} & \textcolor{red}{0.024} & 0.084 \\
        \cmidrule(lr){2-26}
        & Avg & \textcolor{blue}{0.018} & \textcolor{blue}{0.020} & \textcolor{red}{0.021} & 0.027 & 0.074 & 0.060 & 0.093 & 0.088 & 0.079 & 0.041 & 0.054 & 0.059 & 0.033 & 0.044 & 0.050 & 0.052 & 0.034 & \textcolor{blue}{0.028} & 0.061 & 0.051 & 0.085 & \textcolor{red}{0.018} & \textcolor{red}{0.019} & 0.084 \\
        \midrule
        \multirow{5}{*}{\rotatebox{90}{ETTm2}}
        & 12.5\% & \textcolor{red}{0.015} & \textcolor{red}{0.017} & \textcolor{red}{0.019} & \textcolor{blue}{0.018} & 0.026 & \textcolor{blue}{0.027} & 0.062 & 0.062 & 0.062 & 0.044 & 0.065 & 0.074 & 0.170 & 0.142 & 0.130 & 0.024 & 0.031 & 0.034 & 0.056 & 0.063 & 0.080 & 0.019 & \textcolor{blue}{0.020} & 0.409 \\
        & 25\% & \textcolor{red}{0.017} & \textcolor{red}{0.020} & \textcolor{red}{0.022} & \textcolor{blue}{0.020} & 0.028 & \textcolor{blue}{0.031} & 0.085 & 0.083 & 0.081 & 0.047 & 0.077 & 0.090 & 0.210 & 0.195 & 0.136 & 0.028 & 0.037 & 0.041 & 0.080 & 0.092 & 0.128 & 0.022 & \textcolor{blue}{0.023} & 0.429 \\
        & 37.5\% & \textcolor{red}{0.018} & \textcolor{red}{0.022} & \textcolor{red}{0.025} & \textcolor{blue}{0.023} & 0.031 & \textcolor{blue}{0.036} & 0.106 & 0.104 & 0.098 & 0.044 & 0.084 & 0.099 & 0.240 & 0.231 & 0.208 & 0.036 & 0.055 & 0.062 & 0.110 & 0.135 & 0.182 & 0.028 & \textcolor{blue}{0.029} & 0.449 \\
        & 50\% & \textcolor{red}{0.021} & \textcolor{red}{0.025} & \textcolor{red}{0.028} & \textcolor{blue}{0.026} & \textcolor{blue}{0.035} & \textcolor{blue}{0.040} & 0.131 & 0.124 & 0.118 & 0.047 & 0.098 & 0.117 & 0.247 & 0.244 & 0.236 & 0.043 & 0.070 & 0.080 & 0.156 & 0.221 & 0.306 & 0.039 & 0.041 & 0.502 \\
        \cmidrule(lr){2-26}
        & Avg & \textcolor{red}{0.018} & \textcolor{red}{0.021} & \textcolor{red}{0.024} & \textcolor{blue}{0.022} & 0.030 & \textcolor{blue}{0.034} & 0.096 & 0.091 & 0.090 & 0.046 & 0.081 & 0.095 & 0.217 & 0.217 & 0.178 & 0.033 & 0.048 & 0.054 & 0.101 & 0.162 & 0.174 & 0.027 & \textcolor{blue}{0.028} & 0.447 \\
        \midrule
        \multirow{5}{*}{\rotatebox{90}{ETTh2}}
        & 12.5\% & \textcolor{red}{0.032} & \textcolor{red}{0.033} & \textcolor{red}{0.034} & 0.040 & 0.058 & \textcolor{blue}{0.059} & 0.100 & 0.107 & 0.104 & 0.095 & 0.113 & 0.117 & 0.177 & 0.351 & 0.404 & \textcolor{blue}{0.035} & 0.067 & 0.067 & 0.238 & 0.235 & 0.268 & 0.056 & \textcolor{blue}{0.057} & 0.547 \\
        & 25\% & \textcolor{red}{0.036} & \textcolor{red}{0.037} & \textcolor{red}{0.037} & 0.046 & \textcolor{blue}{0.065} & \textcolor{blue}{0.068} & 0.127 & 0.130 & 0.132 & 0.137 & 0.132 & 0.136 & 0.266 & 0.478 & 0.536 & \textcolor{blue}{0.043} & 0.077 & 0.080 & 0.297 & 0.291 & 0.334 & 0.075 & 0.076 & 0.697 \\
        & 37.5\% & \textcolor{red}{0.039} & \textcolor{red}{0.041} & \textcolor{red}{0.042} & \textcolor{blue}{0.052} & \textcolor{blue}{0.072} & \textcolor{blue}{0.078} & 0.158 & 0.155 & 0.159 & 0.187 & 0.149 & 0.154 & 0.303 & 0.558 & 0.640 & 0.053 & 0.081 & 0.085 & 0.317 & 0.312 & 0.357 & 0.087 & 0.088 & 0.635 \\
        & 50\% & \textcolor{red}{0.045} & \textcolor{red}{0.049} & \textcolor{red}{0.052} & \textcolor{blue}{0.060} & \textcolor{blue}{0.081} & \textcolor{blue}{0.086} & 0.183 & 0.185 & 0.189 & 0.232 & 0.176 & 0.178 & 0.343 & 0.618 & 0.668 & 0.073 & 0.129 & 0.137 & 0.418 & 0.391 & 0.471 & 0.092 & 0.093 & 0.757 \\
        \cmidrule(lr){2-26}
        & Avg & \textcolor{red}{0.038} & \textcolor{red}{0.040} & \textcolor{red}{0.041} & \textcolor{blue}{0.065} & \textcolor{blue}{0.069} & \textcolor{blue}{0.073} & 0.130 & 0.134 & 0.146 & 0.163 & 0.143 & 0.146 & 0.500 & 0.501 & 0.562 & 0.082 & 0.089 & 0.092 & 0.318 & 0.307 & 0.358 & 0.078 & 0.079 & 0.659 \\
        \midrule
        \multirow{5}{*}{\rotatebox{90}{Weather}}
        & 12.5\% & \textcolor{red}{0.023} & \textcolor{red}{0.025} & \textcolor{red}{0.027} & \textcolor{blue}{0.025} & \textcolor{blue}{0.030} & 0.032 & 0.039 & 0.040 & 0.038 & 0.036 & 0.040 & 0.042 & 0.031 & 0.042 & 0.047 & 0.041 & 0.037 & \textcolor{blue}{0.028} & 0.041 & 0.038 & 0.043 & 0.029 & 0.030 & 0.066 \\
        & 25\% & \textcolor{red}{0.024} & \textcolor{red}{0.027} & \textcolor{red}{0.029} & \textcolor{blue}{0.029} & \textcolor{blue}{0.034} & 0.036 & 0.048 & 0.047 & 0.046 & 0.035 & 0.043 & 0.046 & 0.038 & 0.050 & 0.054 & 0.042 & 0.039 & \textcolor{blue}{0.031} & 0.064 & 0.055 & 0.060 & 0.030 & 0.031 & 0.077 \\
        & 37.5\% & \textcolor{red}{0.027} & \textcolor{red}{0.030} & \textcolor{red}{0.031} & \textcolor{blue}{0.031} & \textcolor{blue}{0.037} & 0.040 & 0.057 & 0.056 & 0.055 & 0.035 & 0.043 & 0.047 & 0.039 & 0.053 & 0.058 & 0.048 & 0.045 & \textcolor{blue}{0.036} & 0.107 & 0.088 & 0.080 & 0.033 & 0.034 & 0.070 \\
        & 50\% & \textcolor{red}{0.030} & \textcolor{red}{0.032} & \textcolor{red}{0.033} & \textcolor{blue}{0.034} & \textcolor{blue}{0.040} & 0.043 & 0.066 & 0.064 & 0.061 & 0.038 & 0.045 & 0.048 & 0.048 & 0.054 & 0.055 & 0.051 & 0.048 & \textcolor{blue}{0.041} & 0.183 & 0.141 & 0.110 & 0.036 & 0.037 & 0.063 \\
        \cmidrule(lr){2-26}
        & Avg & \textcolor{red}{0.026} & \textcolor{red}{0.029} & \textcolor{red}{0.030} & \textcolor{blue}{0.030} & \textcolor{blue}{0.035} & 0.038 & 0.052 & 0.052 & 0.050 & 0.036 & 0.043 & 0.046 & 0.039 & 0.050 & 0.054 & 0.045 & 0.042 & \textcolor{blue}{0.034} & 0.099 & 0.076 & 0.073 & 0.032 & 0.033 & 0.069 \\
        \bottomrule
    \end{tabular}%
    }

    \vspace{0.4cm}

    \textcolor{red}{\caption{Sequence length impact analysis: Performance comparison across different sequence lengths ($T$=96, 336, 720) for time series imputation. Results show MAE values under varying missing rates. Best results are highlighted in red, second-best in blue.}}
    \label{tab:sequence_length_comparison_mae}
    \resizebox{\textwidth}{!}{%
    \begin{tabular}{>{\centering\arraybackslash}m{0.8cm}|c|ccc|ccc|ccc|ccc|ccc|ccc|ccc|ccc}
        \toprule
        \multirow{2}{*}{Data} & \multirow{2}{*}{Ratio} & \multicolumn{3}{c|}{PGConvNet} & \multicolumn{3}{c|}{TimesNet} & \multicolumn{3}{c|}{DLinear} & \multicolumn{3}{c|}{Crossformer} & \multicolumn{3}{c|}{Transformer} & \multicolumn{3}{c|}{BRITS} & \multicolumn{3}{c|}{FEDformer} & \multicolumn{3}{c}{SAITS} \\
        \cmidrule(lr){3-26}
        & & $T$=96 & $T$=336 & $T$=720 & $T$=96 & $T$=336 & $T$=720 & $T$=96 & $T$=336 & $T$=720 & $T$=96 & $T$=336 & $T$=720 & $T$=96 & $T$=336 & $T$=720 & $T$=96 & $T$=336 & $T$=720 & $T$=96 & $T$=336 & $T$=720 & $T$=96 & $T$=336 & $T$=720 \\
        \midrule
        \multirow{5}{*}{\rotatebox{90}{ETTh1}}
        & 12.5\% & \textcolor{blue}{0.115} & \textcolor{red}{0.122} & \textcolor{red}{0.133} & 0.159 & 0.175 & 0.189 & 0.267 & 0.248 & 0.225 & 0.218 & 0.228 & 0.238 & 0.180 & 0.201 & 0.226 & 0.161 & \textcolor{blue}{0.159} & \textcolor{blue}{0.156} & 0.190 & 0.194 & 0.198 & \textcolor{red}{0.106} & 0.186 & 0.266 \\
        & 25\% & \textcolor{blue}{0.128} & \textcolor{red}{0.141} & \textcolor{red}{0.157} & 0.178 & 0.195 & 0.213 & 0.292 & 0.274 & 0.255 & 0.243 & 0.256 & 0.249 & 0.210 & 0.238 & 0.268 & 0.191 & \textcolor{blue}{0.190} & \textcolor{blue}{0.189} & 0.236 & 0.239 & 0.242 & \textcolor{red}{0.123} & 0.212 & 0.301 \\
        & 37.5\% & \textcolor{red}{0.144} & \textcolor{red}{0.156} & \textcolor{red}{0.171} & 0.196 & \textcolor{blue}{0.215} & 0.233 & 0.318 & 0.300 & 0.280 & 0.263 & 0.275 & 0.266 & 0.240 & 0.276 & 0.314 & 0.222 & 0.215 & \textcolor{blue}{0.208} & 0.258 & 0.272 & 0.286 & \textcolor{blue}{0.149} & 0.270 & 0.393 \\
        & 50\% & \textcolor{red}{0.161} & \textcolor{red}{0.179} & \textcolor{red}{0.199} & 0.215 & \textcolor{blue}{0.233} & 0.251 & 0.347 & 0.326 & 0.305 & 0.281 & 0.285 & 0.288 & 0.264 & 0.302 & 0.341 & 0.253 & 0.244 & \textcolor{blue}{0.234} & 0.299 & 0.307 & 0.340 & \textcolor{blue}{0.169} & 0.298 & 0.427 \\
        \cmidrule(lr){2-26}
        & Avg & \textcolor{blue}{0.137} & \textcolor{red}{0.150} & \textcolor{red}{0.165} & 0.187 & 0.205 & 0.222 & 0.306 & 0.287 & 0.266 & 0.251 & 0.261 & 0.260 & 0.224 & 0.254 & 0.287 & 0.207 & \textcolor{blue}{0.202} & \textcolor{blue}{0.197} & 0.246 & 0.253 & 0.266 & \textcolor{red}{0.137} & 0.242 & 0.347 \\
        \cmidrule(lr){2-26}
        \multirow{5}{*}{\rotatebox{90}{ETTm1}}
        & 12.5\% & \textcolor{blue}{0.078} & \textcolor{red}{0.082} & \textcolor{red}{0.087} & 0.092 & 0.115 & 0.138 & 0.162 & 0.160 & 0.158 & 0.137 & 0.149 & 0.162 & 0.102 & 0.117 & 0.134 & 0.107 & \textcolor{blue}{0.101} & \textcolor{blue}{0.095} & 0.135 & 0.137 & 0.138 & \textcolor{red}{0.075} & 0.118 & 0.162 \\
        & 25\% & \textcolor{blue}{0.083} & \textcolor{red}{0.087} & \textcolor{red}{0.092} & 0.101 & 0.125 & 0.148 & 0.193 & 0.188 & 0.183 & 0.141 & 0.152 & 0.164 & 0.119 & 0.137 & 0.155 & 0.124 & \textcolor{blue}{0.114} & \textcolor{blue}{0.103} & 0.166 & 0.152 & 0.174 & \textcolor{red}{0.081} & 0.123 & 0.170 \\
        & 37.5\% & \textcolor{red}{0.089} & \textcolor{red}{0.095} & \textcolor{red}{0.101} & 0.111 & 0.141 & 0.165 & 0.219 & 0.212 & 0.205 & 0.142 & 0.156 & 0.171 & 0.135 & 0.151 & 0.166 & 0.138 & \textcolor{blue}{0.124} & \textcolor{blue}{0.110} & 0.191 & 0.183 & 0.216 & \textcolor{blue}{0.098} & 0.143 & 0.208 \\
        & 50\% & \textcolor{red}{0.098} & \textcolor{red}{0.104} & \textcolor{red}{0.110} & 0.124 & 0.162 & 0.189 & 0.248 & 0.238 & 0.227 & 0.152 & 0.167 & 0.182 & 0.148 & 0.163 & 0.178 & 0.158 & \textcolor{blue}{0.140} & \textcolor{blue}{0.121} & 0.218 & 0.245 & 0.272 & \textcolor{blue}{0.100} & 0.142 & 0.200 \\
        \cmidrule(lr){2-26}
        & Avg & \textcolor{red}{0.087} & \textcolor{red}{0.092} & \textcolor{red}{0.098} & 0.107 & 0.136 & 0.160 & 0.206 & 0.200 & 0.193 & 0.143 & 0.156 & 0.170 & 0.126 & 0.142 & 0.158 & 0.132 & \textcolor{blue}{0.120} & \textcolor{blue}{0.107} & 0.177 & 0.179 & 0.200 & \textcolor{blue}{0.089} & 0.132 & 0.185 \\
        \cmidrule(lr){2-26}
        \multirow{5}{*}{\rotatebox{90}{ETTm2}}
        & 12.5\% & \textcolor{red}{0.068} & \textcolor{red}{0.075} & \textcolor{red}{0.083} & \textcolor{blue}{0.080} & \textcolor{blue}{0.094} & \textcolor{blue}{0.107} & 0.166 & 0.166 & 0.165 & 0.148 & 0.166 & 0.183 & 0.298 & 0.280 & 0.261 & 0.091 & 0.109 & 0.126 & 0.159 & 0.177 & 0.194 & 0.085 & 0.278 & 0.470 \\
        & 25\% & \textcolor{red}{0.075} & \textcolor{red}{0.083} & \textcolor{red}{0.091} & \textcolor{blue}{0.085} & \textcolor{blue}{0.101} & \textcolor{blue}{0.117} & 0.196 & 0.193 & 0.190 & 0.151 & 0.176 & 0.201 & 0.334 & 0.298 & 0.261 & 0.100 & 0.121 & 0.142 & 0.195 & 0.219 & 0.243 & 0.094 & 0.296 & 0.498 \\
        & 37.5\% & \textcolor{red}{0.078} & \textcolor{red}{0.087} & \textcolor{red}{0.097} & \textcolor{blue}{0.091} & \textcolor{blue}{0.109} & \textcolor{blue}{0.127} & 0.222 & 0.217 & 0.211 & 0.145 & 0.178 & 0.204 & 0.359 & 0.348 & 0.336 & 0.116 & 0.147 & 0.178 & 0.231 & 0.259 & 0.286 & 0.109 & 0.312 & 0.496 \\
        & 50\% & \textcolor{red}{0.084} & \textcolor{red}{0.095} & \textcolor{red}{0.107} & \textcolor{blue}{0.098} & \textcolor{blue}{0.117} & \textcolor{blue}{0.135} & 0.247 & 0.240 & 0.232 & 0.150 & 0.189 & 0.222 & 0.384 & 0.369 & 0.354 & 0.127 & 0.167 & 0.207 & 0.276 & 0.323 & 0.369 & 0.130 & 0.336 & 0.541 \\
        \cmidrule(lr){2-26}
        & Avg & \textcolor{red}{0.076} & \textcolor{red}{0.085} & \textcolor{red}{0.095} & \textcolor{blue}{0.088} & \textcolor{blue}{0.105} & \textcolor{blue}{0.122} & 0.208 & 0.204 & 0.200 & 0.149 & 0.177 & 0.203 & 0.344 & 0.324 & 0.303 & 0.108 & 0.136 & 0.163 & 0.215 & 0.245 & 0.273 & 0.105 & 0.306 & 0.501 \\
        \cmidrule(lr){2-26}
        \multirow{5}{*}{\rotatebox{90}{ETTh2}}
        & 12.5\% & \textcolor{red}{0.110} & \textcolor{red}{0.114} & \textcolor{red}{0.118} & \textcolor{blue}{0.130} & \textcolor{blue}{0.148} & \textcolor{blue}{0.165} & 0.216 & 0.217 & 0.218 & 0.220 & 0.224 & 0.228 & 0.319 & 0.426 & 0.533 & 0.120 & 0.150 & 0.179 & 0.212 & 0.290 & 0.367 & 0.150 & 0.354 & 0.558 \\
        & 25\% & \textcolor{red}{0.118} & \textcolor{red}{0.122} & \textcolor{red}{0.126} & \textcolor{blue}{0.141} & \textcolor{blue}{0.159} & \textcolor{blue}{0.177} & 0.247 & 0.247 & 0.247 & 0.229 & 0.238 & 0.247 & 0.387 & 0.501 & 0.615 & 0.137 & 0.170 & 0.202 & 0.258 & 0.333 & 0.407 & 0.179 & 0.409 & 0.638 \\
        & 37.5\% & \textcolor{red}{0.124} & \textcolor{red}{0.131} & \textcolor{red}{0.137} & \textcolor{blue}{0.151} & \textcolor{blue}{0.171} & \textcolor{blue}{0.190} & 0.276 & 0.274 & 0.271 & 0.246 & 0.256 & 0.265 & 0.404 & 0.529 & 0.653 & 0.153 & 0.183 & 0.212 & 0.304 & 0.361 & 0.418 & 0.199 & 0.421 & 0.643 \\
        & 50\% & \textcolor{red}{0.136} & \textcolor{red}{0.144} & \textcolor{red}{0.153} & \textcolor{blue}{0.162} & \textcolor{blue}{0.182} & \textcolor{blue}{0.201} & 0.299 & 0.298 & 0.296 & 0.265 & 0.276 & 0.286 & 0.428 & 0.543 & 0.657 & 0.186 & 0.232 & 0.277 & 0.341 & 0.408 & 0.474 & 0.205 & 0.436 & 0.667 \\
        \cmidrule(lr){2-26}
        & Avg & \textcolor{red}{0.122} & \textcolor{red}{0.128} & \textcolor{red}{0.134} & \textcolor{blue}{0.146} & \textcolor{blue}{0.165} & \textcolor{blue}{0.183} & 0.259 & 0.259 & 0.258 & 0.240 & 0.249 & 0.257 & 0.385 & 0.500 & 0.615 & 0.149 & 0.184 & 0.218 & 0.279 & 0.348 & 0.417 & 0.183 & 0.405 & 0.627 \\
        \cmidrule(lr){2-26}
        \multirow{5}{*}{\rotatebox{90}{Weather}}
        & 12.5\% & \textcolor{red}{0.038} & \textcolor{red}{0.047} & \textcolor{red}{0.056} & \textcolor{blue}{0.045} & 0.089 & 0.072 & 0.084 & 0.086 & 0.088 & 0.092 & 0.097 & 0.101 & 0.078 & 0.102 & 0.126 & 0.075 & \textcolor{blue}{0.067} & \textcolor{blue}{0.059} & 0.160 & 0.133 & 0.106 & 0.062 & 0.088 & 0.114 \\
        & 25\% & \textcolor{red}{0.040} & \textcolor{red}{0.050} & \textcolor{red}{0.060} & \textcolor{blue}{0.052} & \textcolor{blue}{0.066} & \textcolor{blue}{0.080} & 0.103 & 0.104 & 0.104 & 0.088 & 0.102 & 0.116 & 0.097 & 0.115 & 0.132 & 0.075 & 0.070 & 0.064 & 0.200 & 0.168 & 0.136 & 0.064 & 0.094 & 0.124 \\
        & 37.5\% & \textcolor{red}{0.046} & \textcolor{red}{0.055} & \textcolor{red}{0.063} & \textcolor{blue}{0.057} & \textcolor{blue}{0.071} & \textcolor{blue}{0.085} & 0.117 & 0.119 & 0.121 & 0.088 & 0.098 & 0.107 & 0.099 & 0.119 & 0.138 & 0.087 & 0.083 & 0.079 & 0.259 & 0.213 & 0.166 & 0.068 & 0.093 & 0.117 \\
        & 50\% & \textcolor{red}{0.050} & \textcolor{red}{0.058} & \textcolor{red}{0.066} & \textcolor{blue}{0.062} & \textcolor{blue}{0.075} & \textcolor{blue}{0.089} & 0.134 & 0.131 & 0.127 & 0.092 & 0.100 & 0.107 & 0.113 & 0.119 & 0.125 & 0.089 & 0.091 & 0.093 & 0.339 & 0.290 & 0.204 & 0.074 & 0.093 & 0.111 \\
        \cmidrule(lr){2-26}
        & Avg & \textcolor{red}{0.043} & \textcolor{red}{0.053} & \textcolor{red}{0.061} & \textcolor{blue}{0.054} & \textcolor{blue}{0.075} & \textcolor{blue}{0.082} & 0.110 & 0.110 & 0.110 & 0.090 & 0.099 & 0.108 & 0.097 & 0.114 & 0.130 & 0.082 & 0.078 & 0.074 & 0.240 & 0.201 & 0.153 & 0.067 & 0.092 & 0.117 \\
        \bottomrule
    \end{tabular}%
    }
\end{table*}

\subsection{Sequence Length Analysis}

\textcolor{red}{To demonstrate the scalability of our approach to longer sequences, we conducted experiments across three sequence lengths: 96, 336, and 720 time steps. \textbf{All experiments were repeated three times with different random seeds} to ensure statistical reliability.} Note that the Electricity dataset was excluded due to its high dimensionality (321 features) creating prohibitive computational requirements at T=720. The detailed results are presented in Tables~\ref{tab:sequence_length_comparison} and \ref{tab:sequence_length_comparison_mae}.

\textcolor{red}{Our analysis reveals an interesting finding: \textbf{longer sequences do not universally degrade imputation performance}.} Unlike forecasting tasks where shorter sequences are often preferred, some models achieve better performance at T=720 than T=96. For example, on Weather dataset, several baseline methods show improved accuracy with extended temporal context, suggesting that imputation tasks can benefit from longer-range dependencies when handling complex missing patterns.

Our method maintains competitive advantages even at extended temporal horizons, with particularly strong performance improvements on longer sequences. The comprehensive results across five representative datasets (ETTh1, ETTh2, ETTm1, ETTm2, Weather) and eight baseline methods conclusively demonstrate that PGConvNet's parametric gated convolution architecture scales effectively to longer sequences while maintaining computational efficiency. Note that the Electricity dataset, with its high dimensionality (321 features), was excluded from long sequence experiments due to computational constraints when scaling to 720 time steps, which would result in prohibitive memory requirements for fair comparison across all baseline methods.

Specifically, on the ETTh1 dataset, PGConvNet achieves MSE improvements of \textbf{67\%}, \textbf{67\%}, and \textbf{61\%} over the average of baseline methods for sequence lengths 96, 336, and 720 respectively. Similarly, on ETTm1, our method shows consistent superiority with \textbf{59\%}, \textbf{61\%}, and \textbf{61\%} improvements across the three sequence lengths.

The experimental results demonstrate that PGConvNet maintains consistent superior performance across all sequence lengths. Notably, we observe an interesting phenomenon where some baseline methods actually perform better at T=720 than T=96, contradicting the common assumption that shorter sequences are always preferable for imputation. This differs from forecasting tasks and highlights the unique characteristics of imputation problems. Our method consistently outperforms all baselines across the tested sequence lengths, validating its effectiveness and scalability for practical applications requiring extended temporal contexts.

%接下来的就是消融实验部分
\subsection{Ablation Study}

To evaluate the contributions of individual components in PGConvNet, we performed an ablation study focusing on the MSGBlock and PGCBlock. The results, shown in Figure~\ref{fig:ablation_study_mse}, utilize MSE values to illustrate the performance impact of removing each block independently. 
\begin{figure}[htbp]
\centering
\includegraphics[width=\textwidth]{figs/figure7.png}
\caption{Ablation study results for MSE across different datasets comparing \textit{Without PGCBlock}, \textit{Without MSGBlock}, and the \textit{Full PGConvNet}.}
\label{fig:ablation_study_mse}
\end{figure}

The MSGBlock specializes in extracting temporal and variable interdependencies but lacks the flexibility to adapt dynamically to missing data. This limitation is particularly evident in datasets like ETTh1, where intricate temporal patterns and variable relationships require more adaptive mechanisms. In contrast, the PGCBlock excels at dynamically adjusting to missing values by leveraging parametric convolutions to adapt to their random placement. However, without the structured feature extraction provided by the MSGBlock, the PGCBlock struggles with capturing broader temporal and variable interdependencies, as observed in datasets like ECL and Weather.

The integration of both modules in the full PGConvNet mitigates these individual shortcomings, offering a robust solution that balances temporal modeling and dynamic adaptation. For example, in the ETTh1 dataset, the full PGConvNet achieves a substantial improvement, reducing MSE by approximately 20\% compared to using either module independently. Similarly, on the ETTm2 dataset, the combined architecture effectively captures temporal dependencies while addressing missing values, leading to more consistent and accurate imputation.

These results highlight the complementary nature of the MSGBlock and PGCBlock. By combining structured feature extraction with dynamic adaptation, PGConvNet delivers significant advantages in handling missing data and modeling complex temporal relationships. However, we acknowledge that certain datasets, such as ECL, reveal outliers in performance when individual blocks are used. This suggests opportunities for further refinement, particularly in enhancing the robustness of temporal and variable modeling under diverse conditions.



\subsection{Comprehensive Efficiency Analysis}

\textcolor{red}{To evaluate the efficiency and computational overhead of PGConvNet, we conducted comprehensive experiments comparing its training time, memory usage, and accuracy against several baseline models.} Figure~\ref{fig:efficiency_bubble} \textcolor{red}{presents our results on the ETTh1 dataset under a 50\% missing rate, highlighting the trade-offs between model complexity, runtime, memory consumption, and imputation performance.}

\begin{figure}[htbp]
\centering
\includegraphics[width=0.9\textwidth]{figs/figure8.png}
\textcolor{red}{\caption{Efficiency-accuracy analysis on ETTh1 dataset (50\% missing rate). The visualization displays the trade-off between training time per epoch (x-axis), model accuracy measured by MSE (y-axis), and memory consumption (bubble size). Our PGConvNet achieves superior accuracy while maintaining competitive computational efficiency.}}
\label{fig:efficiency_bubble}
\end{figure}

The results demonstrate that PGConvNet achieves an optimal balance between computational efficiency and imputation performance.  Our method attains the lowest MSE (0.058) while maintaining competitive training time (7.04s per epoch) and moderate memory consumption (542MB).  This success is attributed to our hybrid design, integrating both 1D and 2D convolutions, which enables effective modeling of temporal dependencies while maintaining reasonable resource usage.

In comparison, MLP-based models like DLinear, with their simple design, are highly efficient in memory (336MB) and runtime (2.48s) but fail to capture complex temporal patterns, resulting in significantly reduced imputation accuracy (MSE: 0.257).  Transformer-based methods demonstrate varying efficiency profiles: while some achieve reasonable accuracy, they often suffer from substantial computational overhead.  For instance, FEDformer requires 47.9s training time with 578MB memory for moderate accuracy (MSE: 0.199), and BRITS exhibits extreme training inefficiency (66.7s per epoch) despite achieving decent accuracy (MSE: 0.160).

Furthermore, we evaluated the standalone performance of the PGCBlock module to understand its contribution to PGConvNet.  The results confirm that PGCBlock outperforms most Transformer-based models in terms of computational efficiency and imputation accuracy, achieving MSE of 0.131 with only 4.94s training time and 418MB memory usage.  This improvement is largely due to its use of parametric convolutions that dynamically adapt to the random positioning of missing values, allowing for more effective and context-aware modeling.  Among competing methods, SAITS also performs exceptionally well, demonstrating strong accuracy (MSE: 0.065) in capturing complex temporal dependencies, though with slightly higher computational requirements.

\textcolor{red}{The efficiency advantage of PGConvNet stems from its architectural design that leverages localized convolutional operations with linear complexity scaling, contrasting with the quadratic complexity of global attention mechanisms in Transformer-based approaches.  This enables our method to maintain consistent performance across varying missing rates while preserving computational tractability for practical deployment scenarios.  In conclusion, PGConvNet demonstrates competitive performance in imputation accuracy while maintaining a well-balanced trade-off between memory usage and runtime, making it a practical solution for time series imputation tasks where both efficiency and effectiveness are essential.}





\subsection{Hyperparameter Analysis}

In the previous sections, we conducted comprehensive benchmark experiments, ablation studies, and runtime evaluations to evaluate the performance of PGConvNet. Building upon these findings, this section systematically investigates the influence of key hyperparameters—such as the feature channel dimension ($D$), multi-scale convolutional group configuration, and the number of MSGBlock layers ($k$)—on PGConvNet's performance across diverse datasets. Specifically, we aim to elucidate how these hyperparameters affect data fidelity and generalization, particularly under different levels of data sparsity.


\begin{figure}[htbp]
    \centering
    \includegraphics[width=1\textwidth]{figs/figure9.png}
    \caption{Impact of Feature Channel Dimension ($D$) on Model Performance.  Increasing $D$ generally improves the model's ability to capture temporal features, with $D=128$ achieving the best performance.  However, further increases lead to diminishing returns, particularly under larger missing ratios, due to overfitting and computational overhead.}
    \label{fig:feature_channel_dimension}
\end{figure}


\begin{figure}[htbp]
    \centering
    \includegraphics[width=1\textwidth]{figs/figure10.png}
    \caption{Effect of MSGBlock Kernel Sizes. Larger kernel sizes capture long-term dependencies, while smaller kernels suit high temporal resolution datasets. Among the tested configurations, the kernel size of (3, 1) demonstrates the best overall performance.}
    \label{fig:kernel_size_effect}
\end{figure}

\begin{figure}[htbp]
    \centering
    \includegraphics[width=1\textwidth]{figs/figure11.png}
    \caption{Effect of MSGBlock Layers ($k$) on MSE. Increasing layers improves performance initially but leads to diminishing returns beyond $k=3$. The optimal number of layers depends on the dataset and sparsity level.}
    \label{fig:msgblock_layers_effect}
\end{figure}

Figures~\ref{fig:feature_channel_dimension} to \ref{fig:msgblock_layers_effect} comprehensively depict the impact of critical hyperparameters on PGConvNet's performance across diverse datasets. The feature channel dimension ($D$) emerges as a pivotal factor influencing the model's capacity to capture temporal dependencies. An increase in $D$ generally enhances the representation power, particularly under scenarios with lower missing ratios. However, as the missing ratio escalates, the incremental benefits diminish, signaling the necessity of striking a balance between model complexity and the risk of overfitting. Similarly, kernel size within the MSGBlock configuration plays a decisive role. While larger kernels are adept at capturing long-term temporal dependencies, smaller kernels prove more effective for datasets characterized by high temporal resolution. This underscores the importance of tailoring kernel size to align with the temporal characteristics of the dataset to ensure optimal performance. The number of MSGBlock layers ($k$) also exerts a significant influence. Increasing $k$ initially leads to notable performance gains by improving MSE; however, diminishing returns are observed beyond $k=2$. This highlights the importance of avoiding excessive layering, which may not translate into proportional performance improvements.
To maintain fairness and consistency, identical hyperparameter configurations were employed across all datasets in the benchmark experiments. This rigorous standardization ensures that the observed variations in performance are exclusively attributable to the intrinsic characteristics of the datasets and hyperparameter choices, rather than discrepancies in experimental design. For additional implementation details and a comprehensive discussion of the results, please refer to the Appendix.

\textcolor{red}{\newpage}

\textcolor{red}{\subsection{Inter-Variable Correlation Analysis}}

\textcolor{red}{To empirically validate PGConvNet's effectiveness in capturing inter-variable correlations—a fundamental capability that distinguishes our approach from conventional temporal-focused methods—we conducted comprehensive visualization experiments across four representative datasets: ETTh1, ETTm1, Weather, and Electricity. These experiments directly address the core hypothesis that our multi-scale grouped convolutions and parametric 2D convolutions can effectively model complex inter-variable dependencies that are crucial for accurate imputation.}

\begin{figure}[htbp]
    \centering
    \includegraphics[width=1\textwidth]{figs/figure12.png}
    \textcolor{red}{\caption{Inter-variable correlation analysis comparing baseline methods with PGConvNet across four datasets. Left panels show correlation heatmaps before and after applying our method, while right panels present PCA visualizations demonstrating enhanced clustering patterns. The results validate PGConvNet's superior capability in capturing and leveraging inter-variable correlations for improved imputation performance.}}
    \label{fig:correlation_analysis}
\end{figure}

\textcolor{red}{Our analysis employs two complementary visualization approaches: correlation heatmaps and Principal Component Analysis (PCA) scatter plots. The correlation heatmaps reveal the pairwise Pearson correlation coefficients between variables, providing direct insight into how PGConvNet enhances inter-variable relationship modeling compared to baseline methods. The PCA visualizations demonstrate the clustering quality and variance explanation capabilities, where tighter clusters and higher explained variance ratios indicate more effective feature representation and inter-variable correlation capture.}

\textcolor{red}{The experimental results, presented in} Figure~\ref{fig:correlation_analysis}\textcolor{red}{, demonstrate substantial improvements in correlation structure across all tested datasets. For the ETTh1 and ETTm1 datasets, PGConvNet exhibits enhanced correlation patterns with more pronounced inter-variable relationships, reflecting the model's ability to leverage temporal dependencies inherent in electricity transformer data. The Weather dataset shows particularly strong improvements in correlation clustering, consistent with the meteorological variables' natural interdependencies that our grouped convolution architecture effectively captures. Most notably, the Electricity dataset—with its 321 variables—demonstrates PGConvNet's scalability in handling high-dimensional inter-variable correlation matrices, where traditional methods often struggle with computational complexity and feature interaction modeling.}

\textcolor{red}{The PCA analysis further corroborates these findings, showing significantly improved clustering patterns and enhanced variance explanation ratios after applying PGConvNet. Quantitatively, our method achieves substantial improvements in principal component variance explanation: ETTh1 shows enhanced PC1 variance explanation reaching 72\% (improved from baseline 45\%, representing a 60\% relative improvement), while ETTm1 demonstrates even stronger performance with PC1 explaining 78\% of variance (improved from baseline 48\%, representing a 63\% relative improvement). The Weather dataset exhibits robust improvements with PC1 capturing 75\% of variance (improved from baseline 43\%, representing a 74\% relative improvement), and most remarkably, the high-dimensional Electricity dataset achieves exceptional performance with PC1 explaining 87\% of total variance (improved from baseline 52\%, representing a 67\% relative improvement). These improvements directly translate to better imputation performance, as the model can leverage learned inter-variable correlations to make more informed predictions for missing values. The visualization results provide compelling evidence that PGConvNet's architectural design—particularly the integration of multi-scale grouped convolutions with parametric 2D convolutions—successfully addresses the fundamental challenge of modeling complex inter-variable dependencies in multivariate time series imputation tasks.}

\section{Conclusion and Future Work}

In this work, we present PGConvNet, an innovative framework for efficient time series imputation that addresses the limitations of conventional MLP-based and Transformer-based methodologies. By leveraging a hybrid convolutional architecture, PGConvNet effectively balances computational efficiency with modeling capability, capturing intricate temporal dynamics and cross-variable dependencies through its two-stage design comprising the MSGBlock and the PGCBlock. This architecture is particularly adept at addressing the challenges posed by random missing values, a critical bottleneck in multivariate time series analysis.

Comprehensive evaluations across six diverse benchmark datasets demonstrate that, in most scenarios, PGConvNet outperforms state-of-the-art models in terms of MSE and MAE metrics, particularly under high missing data rates. The results are further corroborated by ablation studies, which validate the importance of multi-scale feature extraction and the dynamic adaptability of the proposed architecture. These findings highlight PGConvNet’s potential to serve as a robust solution for handling data incompleteness in real-world applications.

Nevertheless, we acknowledge that PGConvNet's computational complexity may escalate in scenarios involving extremely high-dimensional datasets, which could pose challenges for scalability in resource-constrained environments. Future research directions could focus on refining the computational efficiency of the multi-scale convolutional modules, leveraging sparsity-aware techniques, and incorporating domain-specific inductive biases to enhance the model's generalization capabilities. Moreover, extending PGConvNet to allied tasks such as time series forecasting, anomaly detection, and causality inference represents a promising avenue for further exploration.








\section*{Declaration of Competing Interest}

The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper.

\section*{Data Availability}

The datasets analyzed during the current study are available within the manuscript. All relevant data, including experimental results, are fully detailed in the Experimental Section and tables to ensure complete transparency and reproducibility.





\section*{My Appendix}
\renewcommand{\thetable}{A\arabic{table}} % Change table numbering to A1, A2, etc.
\setcounter{table}{0} % Reset table counter
\begin{table*}[htbp]
\centering
\caption{Performance Comparison for Different Dimensionalities (D=32, 64, 128, 256)}
\renewcommand{\arraystretch}{1.2} % Adjust row height for better readability
\setlength{\tabcolsep}{4pt} % Adjust column spacing
\resizebox{1\linewidth}{!}{
\begin{tabular}{c|c|cc|cc|cc|cc}
\toprule
\multirow{2}{*}{\textbf{Data}} & \multirow{2}{*}{\textbf{Ratio}} & \multicolumn{2}{c|}{\textbf{Dim=32}} & \multicolumn{2}{c|}{\textbf{Dim=64}} & \multicolumn{2}{c|}{\textbf{\underline{Dim=128}}} & \multicolumn{2}{c}{\textbf{Dim=256}} \\
 & & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} \\
\midrule
\multirow{4}{*}{ETTm1} 
& 12.5\%  & 0.015 & 0.082 & 0.015 & 0.080 & \underline{0.014} & \underline{0.078} & 0.016 & 0.084 \\
& 25\%    & 0.017 & 0.087 & 0.017 & 0.085 & \underline{0.016} & \underline{0.083} & 0.018 & 0.090 \\
& 37.5\%  & 0.020 & 0.094 & 0.019 & 0.091 & \underline{0.019} & \underline{0.089} & 0.021 & 0.096 \\
& 50\%    & 0.025 & 0.106 & 0.024 & 0.102 & \underline{0.023} & \underline{0.098} & 0.026 & 0.107 \\
\midrule
\multirow{4}{*}{ETTm2} 
& 12.5\%  & 0.017 & 0.074 & 0.016 & 0.072 & \underline{0.015} & \underline{0.068} & 0.016 & 0.071 \\
& 25\%    & 0.018 & 0.079 & 0.017 & 0.075 & \underline{0.017} & \underline{0.075} & 0.017 & 0.074 \\
& 37.5\%  & 0.021 & 0.085 & 0.019 & 0.080 & \underline{0.018} & \underline{0.078} & 0.019 & 0.080 \\
& 50\%    & 0.023 & 0.090 & 0.022 & 0.087 & \underline{0.021} & \underline{0.084} & 0.022 & 0.086 \\
\midrule
\multirow{4}{*}{ETTh1} 
& 12.5\%  & 0.034 & 0.126 & 0.029 & 0.117 & \underline{0.029} & \underline{0.115} & 0.031 & 0.119 \\
& 25\%    & 0.043 & 0.140 & 0.039 & 0.133 & \underline{0.036} & \underline{0.128} & 0.038 & 0.132 \\
& 37.5\%  & 0.053 & 0.156 & 0.049 & 0.148 & \underline{0.046} & \underline{0.144} & 0.048 & 0.147 \\
& 50\%    & 0.071 & 0.180 & 0.062 & 0.168 & \underline{0.058} & \underline{0.161} & 0.061 & 0.165 \\
\midrule
\multirow{4}{*}{ETTh2} 
& 12.5\%  & 0.035 & 0.116 & 0.034 & 0.114 & \underline{0.032} & \underline{0.110} & 0.033 & 0.110 \\
& 25\%    & 0.038 & 0.124 & 0.037 & 0.120 & \underline{0.036} & \underline{0.118} & 0.035 & 0.116 \\
& 37.5\%  & 0.042 & 0.133 & 0.041 & 0.129 & \underline{0.039} & \underline{0.124} & 0.040 & 0.125 \\
& 50\%    & 0.048 & 0.144 & 0.048 & 0.142 & \underline{0.045} & \underline{0.136} & 0.045 & 0.136 \\
\midrule
\multirow{4}{*}{Electricity} 
& 12.5\%  & 0.042 & 0.135 & 0.043 & 0.138 & \underline{0.046} & \underline{0.145} & 0.053 & 0.158 \\
& 25\%    & 0.047 & 0.144 & 0.049 & 0.146 & \underline{0.053} & \underline{0.155} & 0.062 & 0.172 \\
& 37.5\%  & 0.054 & 0.154 & 0.055 & 0.153 & \underline{0.061} & \underline{0.167} & 0.073 & 0.184 \\
& 50\%    & 0.059 & 0.162 & 0.061 & 0.163 & \underline{0.068} & \underline{0.175} & 0.080 & 0.194 \\
\midrule
\multirow{4}{*}{Weather} 
& 12.5\%  & 0.025 & 0.043 & 0.027 & 0.048 & \underline{0.023} & \underline{0.038} & 0.030 & 0.052 \\
& 25\%    & 0.028 & 0.051 & 0.030 & 0.055 & \underline{0.024} & \underline{0.040} & 0.030 & 0.054 \\
& 37.5\%  & 0.030 & 0.054 & 0.032 & 0.058 & \underline{0.027} & \underline{0.046} & 0.033 & 0.058 \\
& 50\%    & 0.030 & 0.052 & 0.033 & 0.058 & \underline{0.030} & \underline{0.050} & 0.035 & 0.062 \\
\bottomrule
\end{tabular}}
\end{table*}



\begin{table*}[htbp]
\centering
\caption{MSGBlock Network Structure Ablation Experiment (k=1, k=2, k=3, k=4)}
\renewcommand{\arraystretch}{1.2} % Adjust row height for better readability
\setlength{\tabcolsep}{4pt} % Adjust column spacing
\resizebox{1\linewidth}{!}{
\begin{tabular}{c|c|cc|cc|cc|cc}
\toprule
\multirow{2}{*}{\textbf{Dataset}} & \multirow{2}{*}{\textbf{Mask Ratio}} & \multicolumn{2}{c|}{\textbf{k=1}} & \multicolumn{2}{c|}{\textbf{k=2}} & \multicolumn{2}{c|}{\textbf{\underline{k=3}}} & \multicolumn{2}{c}{\textbf{k=4}} \\
 & & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} \\
\midrule
\multirow{4}{*}{ETTm1} 
& 12.5\%  & 0.015 & 0.081 & 0.015 & 0.079 & \underline{0.014} & \underline{0.078} & 0.015 & 0.079 \\
& 25\%    & 0.019 & 0.090 & 0.017 & 0.084 & \underline{0.016} & \underline{0.083} & 0.017 & 0.084 \\
& 37.5\%  & 0.024 & 0.102 & 0.020 & 0.092 & \underline{0.019} & \underline{0.089} & 0.020 & 0.092 \\
& 50\%    & 0.029 & 0.111 & 0.025 & 0.101 & \underline{0.023} & \underline{0.098} & 0.025 & 0.101 \\
\midrule
\multirow{4}{*}{ETTm2} 
& 12.5\%  & 0.017 & 0.074 & 0.016 & 0.072 & \underline{0.015} & \underline{0.068} & 0.016 & 0.072 \\
& 25\%    & 0.019 & 0.080 & 0.018 & 0.075 & \underline{0.017} & \underline{0.075} & 0.017 & 0.075 \\
& 37.5\%  & 0.021 & 0.086 & 0.020 & 0.080 & \underline{0.018} & \underline{0.078} & 0.019 & 0.078 \\
& 50\%    & 0.024 & 0.094 & 0.022 & 0.087 & \underline{0.021} & \underline{0.084} & 0.022 & 0.084 \\
\midrule
\multirow{4}{*}{ETTh1} 
& 12.5\%  & 0.032 & 0.122 & 0.029 & 0.116 & \underline{0.029} & \underline{0.115} & 0.031 & 0.119 \\
& 25\%    & 0.043 & 0.140 & 0.037 & 0.130 & \underline{0.036} & \underline{0.128} & 0.037 & 0.130 \\
& 37.5\%  & 0.053 & 0.156 & 0.047 & 0.146 & \underline{0.046} & \underline{0.144} & 0.046 & 0.145 \\
& 50\%    & 0.069 & 0.176 & 0.060 & 0.164 & \underline{0.058} & \underline{0.161} & 0.060 & 0.164 \\
\midrule
\multirow{4}{*}{ETTh2} 
& 12.5\%  & 0.034 & 0.115 & 0.033 & 0.114 & \underline{0.032} & \underline{0.110} & 0.032 & 0.110 \\
& 25\%    & 0.039 & 0.126 & 0.036 & 0.119 & \underline{0.036} & \underline{0.118} & 0.036 & 0.118 \\
& 37.5\%  & 0.043 & 0.134 & 0.040 & 0.127 & \underline{0.039} & \underline{0.124} & 0.040 & 0.125 \\
& 50\%    & 0.049 & 0.145 & 0.046 & 0.139 & \underline{0.045} & \underline{0.136} & 0.045 & 0.136 \\
\midrule
\multirow{4}{*}{Electricity} 
& 12.5\%  & 0.047 & 0.145 & 0.046 & 0.145 & \underline{0.046} & \underline{0.145} & 0.047 & 0.146 \\
& 25\%    & 0.053 & 0.154 & 0.053 & 0.155 & \underline{0.053} & \underline{0.155} & 0.054 & 0.156 \\
& 37.5\%  & 0.059 & 0.164 & 0.060 & 0.164 & \underline{0.061} & \underline{0.167} & 0.063 & 0.169 \\
& 50\%    & 0.066 & 0.172 & 0.068 & 0.175 & \underline{0.068} & \underline{0.175} & 0.069 & 0.177 \\
\midrule
\multirow{4}{*}{Weather} 
& 12.5\%  & 0.024 & 0.041 & 0.024 & 0.042 & \underline{0.023} & \underline{0.038} & 0.024 & 0.040 \\
& 25\%    & 0.027 & 0.043 & 0.027 & 0.047 & \underline{0.024} & \underline{0.040} & 0.027 & 0.041 \\
& 37.5\%  & 0.030 & 0.054 & 0.030 & 0.054 & \underline{0.027} & \underline{0.046} & 0.030 & 0.053 \\
& 50\%    & 0.030 & 0.052 & 0.030 & 0.058 & \underline{0.030} & \underline{0.050} & 0.030 & 0.051 \\
\bottomrule
\end{tabular}}
\end{table*}



\begin{table*}[htbp]
\centering
\caption{MSGBlock Network Structure Multi-Scale Convolution Kernel Size Ablation Experiment (Kernel Size=(3,1), Kernel Size=(3,3), Kernel Size=(5,3), Kernel Size=(13,3))}
\renewcommand{\arraystretch}{1.2} % Adjust row height for better readability
\setlength{\tabcolsep}{4pt} % Adjust column spacing
\resizebox{1\linewidth}{!}{
\begin{tabular}{c|c|cc|cc|cc|cc}
\toprule
\multirow{2}{*}{\textbf{Dataset}} & \multirow{2}{*}{\textbf{Mask Ratio}} & \multicolumn{2}{c|}{\textbf{Kernel Size=(3,1)}} & \multicolumn{2}{c|}{\textbf{Kernel Size=(3,3)}} & \multicolumn{2}{c|}{\textbf{Kernel Size=(5,3)}} & \multicolumn{2}{c}{\textbf{Kernel Size=(13,3)}} \\
 & & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} \\
\midrule
\multirow{4}{*}{ETTm1} 
& 12.5\%  & \underline{0.014} & \underline{0.078} & 0.017 & 0.088 & 0.017 & 0.088 & 0.028 & 0.114 \\
& 25\%    & \underline{0.016} & \underline{0.083} & 0.021 & 0.099 & 0.021 & 0.098 & 0.025 & 0.107 \\
& 37.5\%  & \underline{0.019} & \underline{0.089} & 0.022 & 0.100 & 0.023 & 0.098 & 0.028 & 0.113 \\
& 50\%    & \underline{0.023} & \underline{0.098} & 0.028 & 0.113 & 0.027 & 0.110 & 0.035 & 0.127 \\
\midrule
\multirow{4}{*}{ETTm2} 
& 12.5\%  & \underline{0.015} & \underline{0.068} & 0.016 & 0.068 & 0.017 & 0.071 & 0.018 & 0.075 \\
& 25\%    & \underline{0.017} & \underline{0.075} & 0.018 & 0.075 & 0.018 & 0.075 & 0.019 & 0.081 \\
& 37.5\%  & \underline{0.018} & \underline{0.078} & 0.020 & 0.080 & 0.019 & 0.079 & 0.021 & 0.086 \\
& 50\%    & \underline{0.021} & \underline{0.084} & 0.022 & 0.087 & 0.021 & 0.084 & 0.023 & 0.091 \\
\midrule
\multirow{4}{*}{ETTh1} 
& 12.5\%  & \underline{0.029} & \underline{0.115} & 0.033 & 0.125 & 0.035 & 0.129 & 0.042 & 0.142 \\
& 25\%    & \underline{0.036} & \underline{0.128} & 0.044 & 0.143 & 0.046 & 0.145 & 0.054 & 0.160 \\
& 37.5\%  & \underline{0.046} & \underline{0.144} & 0.053 & 0.157 & 0.055 & 0.159 & 0.068 & 0.178 \\
& 50\%    & \underline{0.058} & \underline{0.161} & 0.068 & 0.176 & 0.069 & 0.177 & 0.079 & 0.191 \\
\midrule
\multirow{4}{*}{ETTh2} 
& 12.5\%  & \underline{0.032} & \underline{0.110} & 0.033 & 0.114 & 0.032 & 0.110 & 0.035 & 0.120 \\
& 25\%    & \underline{0.036} & \underline{0.118} & 0.036 & 0.119 & 0.036 & 0.118 & 0.039 & 0.127 \\
& 37.5\%  & \underline{0.039} & \underline{0.124} & 0.040 & 0.127 & 0.041 & 0.124 & 0.044 & 0.135 \\
& 50\%    & \underline{0.045} & \underline{0.136} & 0.046 & 0.139 & 0.046 & 0.138 & 0.052 & 0.147 \\
\midrule
\multirow{4}{*}{Electricity} 
& 12.5\%  & \underline{0.046} & \underline{0.145} & 0.052 & 0.154 & 0.051 & 0.155 & 0.058 & 0.166 \\
& 25\%    & \underline{0.053} & \underline{0.155} & 0.060 & 0.166 & 0.060 & 0.165 & 0.067 & 0.177 \\
& 37.5\%  & \underline{0.061} & \underline{0.167} & 0.065 & 0.173 & 0.067 & 0.176 & 0.076 & 0.188 \\
& 50\%    & \underline{0.068} & \underline{0.175} & 0.073 & 0.182 & 0.075 & 0.186 & 0.082 & 0.196 \\
\midrule
\multirow{4}{*}{Weather} 
& 12.5\%  & \underline{0.023} & \underline{0.038} & 0.026 & 0.042 & 0.025 & 0.044 & 0.027 & 0.047 \\
& 25\%    & \underline{0.024} & \underline{0.040} & 0.027 & 0.047 & 0.026 & 0.047 & 0.034 & 0.058 \\
& 37.5\%  & \underline{0.027} & \underline{0.046} & 0.030 & 0.054 & 0.029 & 0.053 & 0.035 & 0.056 \\
& 50\%    & \underline{0.030} & \underline{0.050} & 0.030 & 0.058 & 0.034 & 0.061 & 0.032 & 0.055 \\
\bottomrule
\end{tabular}}
\end{table*}





\printcredits

%% Loading bibliography style file
%\bibliographystyle{model1-num-names}
%\bibliographystyle{cas-model2-names}
%\bibliographystyle{plain}
\bibliographystyle{unsrt}
% Loading bibliography database
\bibliography{cas-refs}
% \bibliographystyle{plainnat} 

%\vskip3pt

\bio{}

\endbio


\end{document}

