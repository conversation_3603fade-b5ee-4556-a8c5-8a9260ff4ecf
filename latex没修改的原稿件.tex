%% 
%% Copyright 2019-2024 Elsevier Ltd
%% 
%% Version 2.4
%% 
%% This file is part of the 'CAS Bundle'.
%% --------------------------------------
%% 
%% It may be distributed under the conditions of the LaTeX Project Public
%% License, either version 1.2 of this license or (at your option) any
%% later version.  The latest version of this license is in
%%    http://www.latex-project.org/lppl.txt
%% and version 1.2 or later is part of all distributions of LaTeX
%% version 1999/12/01 or later.
%% 
%% The list of all files belonging to the 'CAS Bundle' is
%% given in the file `manifest.txt'.
%% 
%% Template article for cas-dc documentclass for 
%% double column output.

%\documentclass[a4paper,fleqn,longmktitle]{cas-dc}
\documentclass[a4paper,fleqn]{cas-sc}

\usepackage{graphicx}  
\usepackage{float}  
\usepackage{booktabs} 
\usepackage{multirow}  
\usepackage{placeins}
\usepackage[ruled,vlined]{algorithm2e}  % 保留 algorithm2e
\usepackage{amsmath}
\usepackage{multicol}


\usepackage[numbers,sort&compress]{natbib}  % 保留 natbib 进行引用管理

%%%Author definitions
\def\tsc#1{\csdef{#1}{\textsc{\lowercase{#1}}\xspace}}
\tsc{WGM}
\tsc{QE}
\tsc{EP}
\tsc{PMS}
\tsc{BEC}
\tsc{DE}
%%%

\begin{document}



\let\WriteBookmarks\relax
\def\floatpagepagefraction{1}
\def\textpagefraction{.001}
\shorttitle{Efficient Time Series Imputation via Multi-Scale Grouped Parametric Convolutions}
\shortauthors{Ruochen Liu, Mingxin Teng, et~al.}



\title [mode = title]{A Multi-Scale Feature Embedding Framework Using Grouped and Parametric Convolutions for Efficient Time Series Imputation}                      


\author[1]{Ruochen Liu}
\cormark[1]  

\author[1]{Mingxin Teng}  
\author[2]{Junwei Ma}  
\author[1]{Kai Wu}  

\cortext[1]{Corresponding author: Ruochen Liu. Email: <EMAIL>.}





\affiliation[1]{organization={Key Laboratory of Intelligent Perception and Image Understanding of Ministry of Education, Xidian University},  
                city={Xian},
                postcode={710068},
                state={Shanxi},
                country={China}}
\affiliation[2]{organization={The 20th Research Institute of China Electronics Technology Group Corporation},  
                city={Xian},
                postcode={710068},
                state={Shanxi},
                country={China}}
                
                
        




\begin{abstract}
Missing value imputation is a critical challenge in multivariate time series analysis, as incomplete data significantly degrades downstream task performance. Although recent methods employing Multi-Layer Perceptron (MLP) and Transformer-based models have gained attention for capturing non-linear relationships and long-range dependencies, they primarily focus on intrinsic temporal features, such as periodicity and trends, which limits their ability to handle complex, cross-variable interactions. Additionally, these models, often utilizing either simple mappings or attention mechanisms, face challenges in balancing effectiveness and computational efficiency, especially with randomly distributed missing values. To address these limitations, we propose a two-stage network architecture, the Parametric Grouped 
Convolutional Network (PGConvNet), specifically designed for time series imputation. By expanding multivariate time series from 1D to 2D and mapping variable information into higher-dimensional channels, PGConvNet effectively captures both temporal and inter-variable dependencies. The first stage employs the Multi-Scale Grouped Convolutional Block (MSGBlock) to extract multi-scale temporal and multivariate interaction features, while the second stage, the Parametric Grouped Convolutional Block (PGCBlock), dynamically adapts to the random positioning of missing values using parametric convolutions, capturing relevant variable and temporal information around missing data points in place of traditional attention mechanisms. Extensive experiments across multiple datasets demonstrate that PGConvNet not only surpasses state-of-the-art models in accuracy and efficiency but also introduces a robust multi-dimensional convolutional paradigm for multivariate time series imputation, effectively addressing complex imputation scenarios.The source code of our proposed method is available at \url{https://github.com/Tmx158/PGConvNet}.

\end{abstract}



\begin{graphicalabstract}
\includegraphics[width=\textwidth, height=1\textheight, keepaspectratio]{figure2.png}

\noindent \textbf{Graphical Abstract Overview:} This graphical abstract illustrates the novel architecture of Parametric Grouped Convolutional Network(PGConvNet), a groundbreaking framework for efficient time series imputation. The two-stage network combines Multi-Scale Grouped Convolutional Block(MSGBlock) and Parametric Grouped Convolutional Block(PGCBlock) to address the challenges of multivariate time series imputation.

\noindent \textbf{Key Highlights:}
\begin{itemize}
\item Efficiently captures multi-scale temporal and inter-variable interactions through advanced grouped convolution mechanisms, ensuring robust feature representation in complex datasets.
\item Introduces a novel parametric 2D convolution module, replacing traditional attention mechanisms by dynamically adapting to the spatial and temporal positions of missing values.
\item Combines 1D and 2D convolution in a unified hybrid framework, validated through extensive experiments as a state-of-the-art imputation model for incomplete time series data.
\end{itemize}

\end{graphicalabstract}




\begin{keywords}
Time Series Imputation \sep 2D-Inspired Temporal Processing  \sep Multi-Scale Feature Embedding \sep Grouped Parametric Convolutions  
\end{keywords}


\maketitle

\section{Introduction}
Multivariate time series data play a critical role in various fields, including finance\cite{islam2013financial}, healthcare\cite{health}, telecommunication networking\cite{Mobile}, and environmental monitoring\cite{environment}, where accurate forecasting and pattern recognition are essential for decision-making. A persistent issue in these datasets is missing values, which often occur due to sensor malfunctions, human error, or data transmission failures. Such missing values can degrade model performance, introduce bias, and reduce the reliability of downstream analyses.

Traditional methods for handling missing data often involve statistical imputation techniques, such as mean substitution, interpolation, or k-nearest neighbors (KNN), among others.\cite{interpolation,saeipourdizaj2021application,chow1971best,chan1997sequential,flornes1999direct,cabodi2011interpolation,carrizosa2013time}
While these methods are straightforward, they fail to capture the temporal dependencies and complex correlations among variables in multivariate time series, limiting their effectiveness in real-world applications. Moreover, these approaches rely on simplistic assumptions, which may not align with the complex nature of the data.  


\begin{figure}[htbp]
    \centering
    \includegraphics[width=1\textwidth, height=0.25\textheight]{figure1.png}
    \caption{The figure presents the Pearson correlation coefficient heatmaps for three datasets: Weather, Electricity, and ETTh1. To ensure randomness, 96 time points were randomly sampled from each dataset, and the pairwise Pearson correlation coefficients were computed for each variable.}
    \label{fig:pearson_correlation}
\end{figure}



In recent years, deep learning techniques have been extensively applied to missing value imputation in multivariate time series. Among these approaches, MLP\cite{mlp} and Transformer-based architectures\cite{transformer} have become prominent due to their capacity to capture non-linear dependencies and complex temporal patterns. 
For example, DLinear\cite{DLinear_AAAI2023}, effectively leverages MLP to handle time series tasks by learning direct mappings between input and output spaces. Similarly, Informer\cite{informer}extends the Transformer framework by improving efficiency in processing long sequences through its attention mechanism. These models have demonstrated significant success in time series imputation tasks by capturing both temporal dependencies and multivariate relationships.While MLP and Transformer-based models have achieved success in multivariate time series imputation, they often prioritize capturing the temporal 
characteristics of individual variables, such as periodicity, trends, and seasonality. This design limits the model to focusing on single-variable patterns, which may reduce its ability to capture complex interactions across variables.Consequently, these models are limited in fully leveraging the rich dependencies present in multivariate data, reducing their effectiveness in accurately addressing missing values.To better illustrate the correlations among variables within multivariate time series, we conducted experiments across three datasets—Weather, Electricity, and ETTh1\cite{informer}. For each dataset, we randomly selected 96 time points to ensure a representative sample and calculated pairwise Pearson correlation coefficients among the variables, as shown in Figure~\ref{fig:pearson_correlation}. The experimental results demonstrate that, despite the randomness in sampling and variation across datasets, strong inter-variable correlations persist.
Notably, the correlation heatmaps indicate that most variables exhibit stronger correlations with adjacent or neighboring variables, a characteristic consistently observed across different datasets and sampling points. This finding is crucial for multivariate time series imputation.

Moreover, a second critical challenge arises from the random distribution of missing values in time series data.  Transformer models, in particular, rely on attention mechanisms to model relationships across entire sequences.  However, these mechanisms are less effective with randomly positioned missing data points, increasing computational costs and complicating the dynamic adaptation to variable positions of missing values.  As a result, these models often face limitations in both accuracy and efficiency, reducing their applicability in real-world scenarios.

To address these limitations and inspired by the effective design of ModernTCN\cite{ModerTcn}, we expand multivariate time series data from a 1D to a 2D representation by mapping variable information into channels. This design enables our model to capture both temporal and inter-variable dependencies more effectively, enhancing its capacity to learn complex interactions. We also design multi-scale grouped feature extraction blocks to capture both temporal and cross-variable features, resulting in a more robust imputation process. To address the challenges posed by randomly missing values, we propose a parametric convolution approach that adapts dynamically to the positions of missing data points. Unlike Transformer-based models, this method emphasizes relevant temporal and variable contexts near the missing values, allowing the model to manage randomness efficiently without adding to computational costs.

In summary, our contributions are as follows:
\begin{itemize}
\item \textbf{Two-Stage Network for Enhanced Imputation:} We propose PGConvNet, a two-stage convolutional architecture that addresses the limitations of existing models in handling multivariate time series imputation. By leveraging a hybrid of 1D and 2D convolutions, our approach enhances feature extraction, resulting in improved accuracy and robustness against varying missing data patterns. Extensive experiments, including ablation studies and resource efficiency assessments, verify that PGConvNet outperforms state-of-the-art models on most benchmark datasets, achieving a balance between efficiency and accuracy.

\item \textbf{Precise Extraction of Temporal and Inter-Variable Features:} To tackle the challenge of capturing temporal dynamics and variable interactions, we introduce the MSGBlock, designed to learn hierarchical, multi-scale patterns for efficient extraction of temporal and variable information.

\item \textbf{Dynamic Adaptation to Missing Value Positions:} To address the challenges posed by the random distribution of missing values, we propose the PGCBlock, which utilizes parametric convolutions to focus on relevant temporal and variable information surrounding missing data points, providing an effective alternative to traditional attention mechanisms for handling randomness in time series data.
\end{itemize}


\section{Related Work}
In recent years, foundational models that capture temporal patterns and can be applied across multiple tasks have gained popularity in time series analysis. Originally developed for tasks like forecasting, these models offer versatility and, in some cases, even surpass specialized imputation models by effectively learning temporal dependencies\cite{du2024tsibenchbenchmarkingtimeseries}. To provide a comprehensive perspective, we review relevant imputation models, general-purpose foundational models, and forecasting models, categorizing these time series models into the following five types.

\textbf{RNN-based Methods:} Recurrent Neural Network (RNN)\cite{rnn}, including Long Short-Term Memory (LSTM)\cite{LSTM} and Gated Recurrent Unit (GRU) networks, were once popular for modeling temporal dependencies. Models like Gated Recurrent Unit with Decay (GRU-D) \cite{GRU-D} adapted RNN for missing data through decay mechanisms.Additionally, Bidirectional Recurrent Imputation for Time Series (BRITS) \cite{brits}employs a bidirectional RNN approach to handle missing values, effectively capturing both forward and backward temporal dependencies.However, issues such as vanishing gradients and limited ability to model long-term dependencies have led to a decline in their use compared to more recent methods.


\textbf{MLP-based Methods:} MLP-based methods have gained traction for their simplicity and efficiency. For instance, RLinear \cite{Rlinear} and DLinear\cite{DLinear_AAAI2023}avoid recurrent structures by relying on direct input-output mappings, making them scalable and resource-efficient for large datasets. Beyond these, recent studies further demonstrate the potential of MLP-based architectures in multivariate time series imputation. For example, one study \cite{longterm_imputation} proposes a deep learning model based on MLP, specifically designed for imputing long-term missing values in multivariate time series, including consecutive months of daily observations, rather than addressing random missing points. Another study \cite{multiscale_mlp_mixer} introduces a novel approach leveraging multi-scale decomposition and an MLP-Mixer architecture, with extensive experiments proving its effectiveness across diverse time series datasets. Collectively, these innovations underscore the flexibility and effectiveness of MLP-based models in tackling complex imputation tasks for large-scale datasets.

\textbf{Transformer-based Methods:} Transformers, originally developed for Natural Language Processing(NLP), excel in time series tasks due to self-attention mechanisms that capture long-range dependencies. Models like PatchTST \cite{PatchTST}, Crossformer \cite{Crossformer}, and FEDformer \cite{fedformer} effectively model temporal patterns across scales, solidifying their role in time series research. In particular, Self-Attention-based Imputation for Time Series(SAITS)\cite{DU} employs diagonal masking within self-attention blocks along with joint learning optimization to accurately impute missing values in time series data, further enhancing Transformer-based methods’ capability in handling incomplete data.

\textbf{CNN-based Methods:} Convolutional Neural Network (CNN) efficiently capture local dependencies. Temporal Convolutional Network (TCN) \cite{tcn} used dilated convolutions for long-range modeling, while TimesNet \cite{timesnet} expanded data dimensionality from a periodic perspective to enhance feature extraction. ModernTCN \cite{ModerTcn} advanced this by employing large kernel attention mechanisms and modernized convolutional structures, achieving state-of-the-art imputation performance.

\textbf{Other Innovative Approaches:} Beyond traditional deep learning methods, several alternative approaches have proven effective in imputation tasks. The OMCI framework\cite{OMDCI}, combining clustering with collective intelligence, achieves promising results for gene expression data imputation. The Generative Broad Bayesian (GBB)\cite{GBB} imputer iteratively imputes missing data using a scalable Bayesian network, enabling uncertainty quantification. RM-MVI\cite{MVI} applies a two-stage optimization strategy with structural and empirical risk minimization to handle missing values in labeled data. Sparse Regression Imputation (SRI)\cite{SRI}, using sparse least squares with iterative methods, performs well on time series and tabular data. Lastly, the Granular Data Imputation method\cite{Granulardataimputation}, utilizing granular computing and fuzzy clustering, is effective for preserving data structure in incomplete datasets.  

Inspired by these advancements, our work seeks to further explore the dimensional expansion in time series modeling. We propose a novel approach that combines 1D and 2D convolutions within the same framework to handle multivariate time series data. While existing work has primarily focused on one-dimensional convolutions or limited extensions to higher dimensions, our method introduces a more comprehensive hybrid architecture that fully leverages the advantages of both 1D and 2D convolutional operations. This combination allows our model to capture temporal and feature-level interactions in a more detailed and nuanced manner, offering a new perspective on how CNN-based architectures can be applied to time series imputation tasks.

\section{PGConvNet}

PGConvNet addresses the challenges of multivariate time series imputation by combining dimensional expansion with hybrid convolutional structures. Through the integration of MSGBlock for multi-scale feature extraction and PGCBlock for dynamic adaptation to missing values, PGConvNet effectively captures complex temporal patterns and enhances robustness. The overall structure is illustrated in Figure~\ref{fig:pgconvnet}.


\begin{figure*}[htbp]
    \centering
    \includegraphics[width=1\textwidth]{figure2.png}
    \caption{The architecture of PGConvNet, comprising two stages: the MSGBlock captures multivariate and temporal dependencies, while the PGCBlock dynamically adapts to missing values for precise imputation. This design achieves state-of-the-art performance on unified benchmark datasets.}

    \label{fig:pgconvnet}
\end{figure*}

\subsection{Feature Embedding Layer}

The Feature Embedding Layer plays a crucial role in our architecture by expanding the dimensionality of the input time series from a 1D to a 2D representation, which allows the network to capture richer feature interactions. This layer is designed to project each variable (or feature) of the time series into a higher-dimensional space, enabling the model to better learn the dependencies between different time steps and variables.

Consider an input time series \( \mathbf{X} \in \mathbb{R}^{T \times N} \), where \( T \) represents the number of time steps (i.e., the length of the time window), and \( N \) is the number of variables for each time step. Each variable at time step \( t \) corresponds to a specific value within a given feature. The goal of the Feature Embedding Layer is to project each variable \( \mathbf{X} \in \mathbb{R}^{T \times N} \) into a higher-dimensional space \( \mathbb{R}^{T \times N \times D} \), where \( D \) represents the dimensional embedding for each variable, thus enhancing the network’s ability to capture more granular information.

Many existing models, such as MLP-based and Transformer-based architectures, have adopted similar strategies for time series data by embedding variable dimensions. For example, Transformer variants commonly map \( \mathbf{X} \in \mathbb{R}^{T \times N} \) to \( \mathbf{X}' \in \mathbb{R}^{T \times D} \), where \( N \rightarrow D \) transforms the variable dimension into channels. This approach benefits from modeling interactions across all variables, allowing the network to exploit dependencies across the entire feature set. However, this method suffers when the number of variables \( N \) grows large, leading to potential information loss due to variable mixing. In such cases, variables may lose their individual representation, and critical temporal patterns could be obscured.

To address this issue, recent research has explored the concept of Channel Independence (CI). For example, the PatchTST model introduced CI\cite{PatchTST}, which demonstrated through experiments that maintaining variable independence can improve performance, particularly in large-scale time series datasets. Building on this, other models like ModerTCN\cite{ModerTcn}
 explored channel independence in convolutional architectures, where each variable is processed independently to preserve its unique contribution.

In this paper, we  incorporating variable-wise independence into the Feature Embedding Layer.  Specifically, we transform each variable \( \mathbf{X} \in \mathbb{R}^{T \times N} \) into a higher-dimensional representation \( \mathbf{X}' \in \mathbb{R}^{T \times N \times D} \). The transformation is performed independently for each variable, preserving variable independence while enhancing the ability to learn complex feature interactions. 

The process can be described as:
\begin{equation}
    \mathbf{X}' = \text{Linear}(\text{Unsqueeze}(\mathbf{X}))
\end{equation}


where the Unsqueeze operation reshapes the original input \( \mathbf{X} \in \mathbb{R}^{T \times N} \) into \( \mathbf{X}_{\text{expanded}} \in \mathbb{R}^{T \times N \times 1} \), and the Linear operation projects each element independently to an embedding space of dimension \( D \). This design ensures that each variable retains its individual characteristics while acquiring richer feature representations.

Our approach avoids the need for complex operations such as downsampling layers and convolutional patching. Instead, it relies on a direct mapping, which simplifies the transformation process, reduces computational overhead, and preserves variable independence for more effective feature extraction.


\subsection{MSG Block}

Before applying 2D convolutions, the introduction of the MSGBlock is necessary to further refine temporal dependencies and capture feature relationships in a hierarchical manner. The use of 1D convolutions within this block is motivated by the need to first capture local temporal features and dependencies at different scales. Time series imputation tasks, particularly those involving missing data, require models to adapt to both short-term variations and long-term dependencies. The 1D convolution layer in the MSGBlock serves to extract these temporal dynamics effectively, as it processes the time dimension directly without mixing feature dimensions prematurely.

Upon embedding, the output tensor of the feature embedding layer $\mathbf{X}' \in \mathbb{R}^{B \times T \times N \times D}$ is reshaped to $\mathbf{X}'' \in \mathbb{R}^{B \times (N \times D) \times T}$ before entering the convolutional layers.
\begin{equation}
\mathbf{X}'' = \text{Reshape}(\mathbf{X}') \in \mathbb{R}^{B \times (N \times D) \times T}
\end{equation}
 \(T\) is moved to the final dimension to align with the convolution operation, allowing the network to efficiently operate on the time dimension in subsequent stages. This configuration ensures that temporal dependencies are fully captured before deeper interactions between feature channels are modeled.

\begin{figure*}[htbp]
    \centering
    \includegraphics[width=1\textwidth]{figure3.png}
   \caption{MSGBlock architecture, featuring Large and Small Kernel Group Conv Blocks with rearrange layers.  Depthwise and grouped convolutions in the Large Kernel Block extract temporal dependencies, followed by reshaping in the Rearrange Layer.  The Small Kernel Block refines features, and a residual connection passes the output to the next network stage.}

    \label{fig:msgblock}
\end{figure*}

\subsubsection{Large Kernel Group Conv Block}
The first core component within the MSGBlock is the Large Kernel Group Conv Block, which consists of two main convolution operations. Both convolutions use a large kernel  (details on kernel size selection can be found in the extensive experiments discussed in Chapter 4).

Depthwise Convolution: The first convolution applies depthwise convolutions across each channel in $\mathbf{X}'' \in \mathbb{R}^{B \times (N \times D) \times T}$, processing each of the $N \times D$ channels independently along the time axis. The motivation behind using depthwise convolution ( \(\text{groups} = N \times D\)) is to capture fine-grained temporal features for each channel individually. By isolating each channel, the model ensures that important temporal patterns unique to each feature are not diluted by interactions with other channels prematurely:
\begin{equation}
\text{DepthwiseConv}(\mathbf{X}'') = \mathbf{X}_1'' \in \mathbb{R}^{B \times (N \times D) \times T}
\end{equation}

Grouped Convolution: The second convolution is a grouped convolution, where the \(N \times D\) channels are divided into \(N\) groups ( \(\text{groups} = N\)). Each group corresponds to the channels within a single variable. This design is crucial as it allows the model to capture inter-channel interactions within each group while maintaining computational efficiency. By dividing the channels into groups, the convolution operation facilitates localized interactions between channels of the same variable. This enhances the model's ability to learn joint temporal-feature dependencies across multiple scales:
\begin{equation}
\text{GroupedConv}(\mathbf{X}_1'') = \mathbf{X}_2'' \in \mathbb{R}^{B \times (N \times D) \times T}
\end{equation}
This combination of depthwise and grouped convolutions provides a hierarchical mechanism for capturing both intra-channel temporal dependencies and inter-channel relationships within the same variable, allowing for a more nuanced feature extraction process.

\subsubsection{Activation Layer}
After the convolutional operations, an Activation Layer employing the Gaussian Error Linear Unit (GELU) is applied to enhance the model's robustness and capacity for modeling non-linear relationships. The GELU activation function improves the network's ability to capture complex patterns by smoothly approximating the rectified linear behavior:
\begin{equation}
    \mathbf{X}_{\text{activated}} = \text{GELU}(\mathbf{X}_{\text{conv}})
\end{equation}

where $\mathbf{X}_{\text{conv}}$ is the output from the convolutional operations.



\subsubsection{Rearrange Layer}
After the convolution operations, the output tensor is reshaped and permuted to adjust the channel dimensions for the next stage.  This step is essential because passing features from large-kernel convolutions directly to small-kernel convolutions without reordering can lead to suboptimal feature interactions. If the large-kernel convolution features are sent directly to subsequent layers, the temporal and feature-level dependencies may become entangled.  This entanglement can result in information loss.  By permuting the dimensions, the rearrange operation ensures that variable-level and temporal information are processed separately and correctly.  This adjustment preserves the structure of the extracted features and prepares them for optimal alignment in the next convolution block.

\subsubsection{Small Kernel Group Conv Block}
In parallel to the Large Kernel Group Conv Block, the Small Kernel Group Conv Block operates similarly but uses smaller kernel sizes, focusing on very localized feature extraction. The small kernel convolutions handle fine-grained interactions within a narrow temporal context, complementing the large kernel convolutions that capture long-range dependencies.Similar to the large kernel block, the small kernel block employs both depthwise and grouped convolutions. The depthwise convolution processes each channel independently:
\begin{equation}
\text{DepthwiseConv}(\mathbf{X}_2'') = \mathbf{X}_3'' \in \mathbb{R}^{B \times (N \times D) \times T} \end{equation}
while the grouped convolution divides the channels into \( N \) groups:
\begin{equation}
\text{GroupedConv}(\mathbf{X}_3'') = \mathbf{X}_4'' \in \mathbb{R}^{B \times (N \times D) \times T} \end{equation}

This multi-scale design allows the model to adapt to varying temporal patterns across different datasets and tasks. By learning both long-range and short-range dependencies, the MSGBlock enables the network to perform robust imputation, even in cases where missing values occur in complex temporal contexts.

\subsubsection{Final Rearrangement and Residual Connection}
The output from the Small Kernel Group Convolutional Block is rearranged back to its original format. This rearrangement is followed by a residual connection, which serves to retain the input features and support effective gradient propagation throughout the network.
\begin{equation}
    \mathbf{X}_{\text{final}} = \mathbf{X}_{\text{rearranged}} + \mathbf{X}_{\text{input}}
\end{equation}

$\mathbf{X}_{\text{input}}$ is the original input to the MSGBlock, ensuring robustness against vanishing gradients.
To illustrate the detailed processing steps of the MSGBlock, the complete algorithmic framework is outlined below:
\begin{algorithm}[htbp]
\SetAlgoNlRelativeSize{-1}
\SetAlgoNlRelativeSize{0}
\caption{\textbf{MSGBlock for Time Series Feature Extraction}}
\KwIn{
    \textbf{Input tensor} $\mathbf{X}' \in \mathbb{R}^{B \times T \times N \times D}$ \\
    \textbf{Depthwise convolution kernel} $W_d$ \\
    \textbf{Grouped convolution kernel} $W_g$ \\
}
\KwOut{
    \textbf{Processed tensor} $\mathbf{X}_{\text{final}} \in \mathbb{R}^{B \times T \times N \times D}$ \\
}

\tcp{\textbf{Step 1: Reshape input for temporal processing}}
$\mathbf{X}'' \leftarrow \text{Reshape}(\mathbf{X}') \in \mathbb{R}^{B \times (N \times D) \times T}$\;

\tcp{\textbf{Step 2: Apply Depthwise Convolution to capture temporal features}}
$\mathbf{X}_{1}'' \leftarrow \text{DepthwiseConv}(\mathbf{X}'', W_d)$\;

\tcp{\textbf{Step 3: Apply Grouped Convolution for inter-channel interactions}}
$\mathbf{X}_{2}'' \leftarrow \text{GroupedConv}(\mathbf{X}_{1}'', W_g)$\;

\tcp{\textbf{Step 4: Activation Layer with GELU}}
$\mathbf{X}_{\text{activated}} \leftarrow \text{GELU}(\mathbf{X}_{2}'')$\;

\tcp{\textbf{Step 5: Rearrange dimensions for next processing stage}}
$\mathbf{X}_{\text{rearranged}} \leftarrow \text{Rearrange}(\mathbf{X}_{\text{activated}})$\;

\tcp{\textbf{Step 6: Apply Small Kernel Grouped Convolution Block}}
$\mathbf{X}_{\text{small}} \leftarrow \text{SmallKernelGroupConv}(\mathbf{X}_{\text{rearranged}})$\;

\tcp{\textbf{Step 7: Final rearrangement and add residual connection}}
$\mathbf{X}_{\text{final}} \leftarrow \mathbf{X}_{\text{small}} + \mathbf{X}'$\;

\Return $\mathbf{X}_{\text{final}}$
\end{algorithm}



\subsection{PGCBlock}

In the previous section, we discussed how the MSGBlock effectively extracts temporal and inter-variable features through a hierarchical multi-scale approach. However, while feature extraction is crucial, the core challenges differ between tasks such as prediction and imputation in multivariate time series.

\subsubsection{Adapting to Random Missing Values}

Prediction tasks in multivariate time series typically follow a well-defined sequence where the objective is to forecast future values based on a given window of past observations. Formally, this process can be expressed as:
\begin{equation}
    \hat{X}_{t+k} = g(X_{t}, X_{t-1}, \ldots, X_{t-n}),
\label{eq:forecasting}
\end{equation}
\( \hat{X}_{t+k} \) denotes the predicted value at time \( t+k \), and \( X_{t}, X_{t-1}, \ldots, X_{t-n} \) represent a fixed sequence of past observations used to make the prediction. In this case, the positions of the observations are known and fixed, allowing the model to leverage all available temporal information within the input window.

In contrast, imputation tasks require a fundamentally different approach, as missing values appear randomly throughout the time series, rather than following a predictable or sequential pattern. This randomness necessitates a model that can dynamically capture the temporal and inter-variable relationships surrounding the missing values. The imputation task can be formulated as follows:
\begin{equation}
\hat{X}_{v,t_i} = h\left( 
\begin{bmatrix}
X_{1,t_1} & \mkern-6mu \cdots \mkern-6mu & X_{1,t_{i-1}} & \mkern-6mu \cdots \mkern-6mu & \textit{NaN} & \mkern-6mu \cdots \mkern-6mu & X_{1,t_T} \\
X_{2,t_1} & \mkern-6mu \cdots \mkern-6mu & X_{2,t_{i-1}} & \mkern-6mu \cdots \mkern-6mu & \textit{NaN} & \mkern-6mu \cdots \mkern-6mu & X_{2,t_T} \\
X_{3,t_1} & \mkern-6mu \cdots \mkern-6mu & X_{3,t_{i-1}} & \mkern-6mu \cdots \mkern-6mu & \textit{NaN} & \mkern-6mu \cdots \mkern-6mu & X_{3,t_T} \\
\end{bmatrix}
\right)
\label{eq:imputation}
\end{equation}

Here, \( \hat{X}_{v,t_i} \) represents the imputed value at time \( t_i \) for variable \( v \), with function \( h \) capturing essential temporal and cross-variable dependencies. Unlike prediction tasks, where sequences are fixed and known, imputation requires the model to dynamically adjust based on the positions of missing values, considering both temporal sequences and inter-variable relationships.The random nature of missing values makes it vital for the model to capture localized interactions between temporal and feature dimensions. Not all temporal and variable-level features are equally relevant; thus, the model must selectively focus on the local context around each missing value. Instead of relying on a fixed global perspective, dynamic adaptation ensures it captures the most informative interactions for precise imputation.

Attention mechanisms can handle missing values by weighing sequence-wide dependencies, but they have two main drawbacks. First, they process the entire input, leading to high computational costs, especially in large multivariate time series where the attention matrix scales quadratically, making them inefficient for large-scale imputation. Second, while capturing global patterns, they often miss critical local interactions, diluting essential dependencies near missing values and reducing imputation accuracy.
In this work, we propose a localized approach using parametric grouped convolutions. PGCBlock focuses computation on regions around missing values, reducing unnecessary processing of observed data while effectively capturing key temporal and inter-variable dependencies.

Inspired by Conditional Convolution(CondConv) \cite{yang2019condconv},which adjusts convolution weights based on input context, we adapted this mechanism for time series imputation. Unlike its original design for 2D spatial tasks, our PGCBlock dynamically recalibrates kernels based on temporal and feature contexts, enhancing local feature extraction and computational efficiency.By selectively adapting to the local context of missing values, PGCBlock balances precision and scalability, efficiently addressing the complexities of large-scale time series. Figure~\ref{fig:pgcblock_diagram} illustrates its core architecture, including pointwise convolution, routing, and dynamic kernel selection tailored for imputation tasks.



\subsubsection{PGCBlock Architecture}
 PGCBlock consists of three main components: the Pointwise Convolution Layer, Routing Sigmoid Layer, and the Dynamic Kernel Selection Mechanism. Each of these elements contributes to capturing both temporal and inter-variable relationships to improve imputation accuracy.
 \begin{figure}[htbp]
    \centering
    \includegraphics[width=1\textwidth]{figure4.png}
   \caption{The PGCBlock architecture. It begins with a Pointwise Convolution Layer to extract initial spatiotemporal features, followed by a Flatten Layer. Routing sigmoid layers dynamically select relevant convolution kernels \( \omega_1, \omega_2, \ldots, \omega_n \) based on the context around missing values. This focuses computation on local regions, enhancing imputation accuracy while maintaining efficiency by reducing unnecessary calculations on observed data.}

    \label{fig:pgcblock_diagram}
\end{figure}

\paragraph{Pointwise Convolution Layer:}
This layer acts as the initial feature extractor, capturing basic temporal and inter-variable correlations from the input data. Given the input tensor \( X \in \mathbb{R}^{T \times V} \), where \( T \) represents the time dimension and \( V \) the number of variables, the pointwise convolution can be formulated as:
\begin{equation}
Y_{t,v} = \sum_{i,j} W_{i,j} X_{t-i,v-j}
\end{equation}
where \( W_{i,j} \) represents the convolution filter applied to the input data at temporal and feature positions \( (i,j) \). This layer focuses on local feature extraction, prioritizing immediate temporal and inter-variable dependencies without considering the global sequence.

\paragraph{Global Pooling:}
After the initial convolution, the extracted feature maps \( Y_{t,v} \) are passed through a global pooling operation to reduce the dimensionality while summarizing essential temporal and inter-variable dependencies:
\begin{equation}
Z_{t,v} = \text{GlobalPool}(Y_{t,v}),
\end{equation}
where \( Z_{t,v} \in \mathbb{R}^C \) represents a condensed version of the extracted features. This pooling operation captures the essential structure of the data without needing to process each individual element.

\paragraph{Routing Sigmoid Layer:}
The next step in PGCBlock is the routing layer, responsible for dynamically adjusting the convolutional kernels based on the context of missing values. The output of the global pooling \( Z_{t,v} \) is processed through a routing function, which outputs the routing weights \( \omega \in \mathbb{R}^N \), where \( N \) represents the number of experts (convolutional kernels):
\begin{equation}
\omega = \sigma(W_r Z_{t,v} + b_r),
\end{equation}
where \( W_r \) and \( b_r \) are learnable parameters and \( \sigma(\cdot) \) is the sigmoid function, which ensures that the routing weights remain in the range \( [0, 1] \). These routing weights dictate how much influence each kernel will have based on the local context around the missing values.

\paragraph{Dynamic Kernel Selection:}
Using the routing weights \( \omega \), the model dynamically selects the most relevant kernels to focus on the local regions around missing data points. For each input, the convolution output is computed as a weighted sum of the kernels:
\begin{equation}
\hat{Y}_{t,v} = \sum_{k=1}^{N} \omega_k W_k * X_{t,v},
\end{equation}
where \( W_k \) represents the \( k \)-th convolution kernel and \( * \) denotes the convolution operation. By using this weighted combination of kernels, the model can adaptively emphasize different temporal and inter-variable patterns depending on the local context of the missing data.

\paragraph{Final Imputed Output:}
Finally, the outputs from all kernels are concatenated to form the final imputed value for each time step and variable:
\begin{equation}
\hat{X}_{t,v} = \text{Concat}(\hat{Y}_{t,v}^{1}, \hat{Y}_{t,v}^{2}, \ldots, \hat{Y}_{t,v}^{N}),
\end{equation}
which provides the model with a robust representation that captures local dependencies while maintaining computational efficiency.Algorithm~\ref{alg:PGCBlock} provides the pseudocode for the PGCBlock.

\begin{algorithm}[htbp]
\SetAlgoNlRelativeSize{-1}
\SetAlgoNlRelativeSize{0}
\caption{\textbf{Parametric Grouped Convolutional Block (PGCBlock) for Time Series Imputation}}
\label{alg:PGCBlock}
\KwIn{
    \textbf{Input data} $X \in \mathbb{R}^{T \times V}$ \\
    \textbf{Mask matrix} $M \in \{0,1\}^{T \times V}$ \\
    \textbf{Number of experts} $N$ \\
    \textbf{Routing function} $r(\cdot)$ \\
    \textbf{Convolutional kernels} $W_k$, $k=1,\ldots,N$ \\
}
\KwOut{
    \textbf{Imputed data} $\hat{X} \in \mathbb{R}^{T \times V}$ \\
}

\For{each time step $t = 1, \ldots, T$ and variable $v = 1, \ldots, V$}{
    \tcp{\textbf{Step 1: Initial feature extraction via Pointwise Convolution}}
    $Y_{t,v} \leftarrow \text{PointwiseConv}(X_{t,v})$\;
    
    \tcp{\textbf{Step 2: Global pooling to summarize features}}
    $Z_{t,v} \leftarrow \text{GlobalPool}(Y_{t,v})$\;
    
    \tcp{\textbf{Step 3: Compute routing weights using sigmoid activation function}}
    $\omega \leftarrow \sigma(W_{r} Z_{t,v} + b_r)$\;
    
    \tcp{\textbf{Step 4: Dynamically apply convolutional kernels with routing weights}}
    $\hat{Y}_{t,v} \leftarrow \sum_{k=1}^{N} \omega_k W_k * X_{t,v}$\;
    
    \tcp{\textbf{Step 5: Concatenate outputs from all kernels}}
    $\hat{X}_{t,v} \leftarrow \text{Concat}(\hat{Y}_{t,v}^{1}, \hat{Y}_{t,v}^{2}, \ldots, \hat{Y}_{t,v}^{N})$\;
}
\Return $\hat{X}$
\end{algorithm}








\newpage
\section{Experiments}
In this section, we evaluate the performance of our proposed PGConvNet on several benchmark time series datasets. The datasets are sourced from the GitHub community repository maintained by Tsinghua University, which is widely used for time series research. Detailed information can be found at \url{https://github.com/thuml/Time-Series-Library}.\cite{wang2024tssurvey} Our experiments cover various settings and provide comprehensive comparisons against state-of-the-art models.

\subsection{Datasets}
We use six benchmark datasets that are frequently used in long-term forecasting and imputation tasks. These datasets vary in terms of sample size, number of features, and data granularity, providing a robust basis for testing the generalizability of our model. The details of each dataset are summarized in Table~\ref{table:datasets}. 
\begin{table}[htbp]
    \centering
    \renewcommand{\arraystretch}{1.2} 
    \setlength{\tabcolsep}{4pt} 
    \caption{Summary of the datasets used in our experiments.}
    \label{table:datasets}
    \begin{tabular}{lcccc}
        \hline
        \textbf{Datasets} & \textbf{ETTh1\&h2} & \textbf{ETTm1\&m2} & \textbf{Electricity} & \textbf{Weather} \\
        \hline
        Samples & 17,420 & 69,680 & 26,304 & 52,696 \\
        Features & 7 & 7 & 321 & 21 \\
        Granularity & 1 hour & 15 min & 1 hour & 10 min \\
        \hline
    \end{tabular}
\end{table}





\paragraph{Description of Datasets:}
\begin{itemize}
    \item \textbf{ETTh1 \& ETTh2:} These datasets consist of hourly data from electric transformers, capturing seven load characteristics of oil and power transformers from July 2016 to July 2018.
    \item \textbf{ETTm1 \& ETTm2:} These datasets provide 15-minute resolution data, similar to the ETTh datasets, covering the same time period and variables.
    \item \textbf{Electricity:} This dataset contains hourly electricity consumption data for 321 customers, collected from 2012 to 2014.
    \item \textbf{Weather:} This dataset includes 21 weather indicators, such as temperature and humidity, recorded every 10 minutes in Germany during 2020.
\end{itemize}

\subsection{Evaluation Metrics}
To assess the performance of the imputation models, we use the following metrics:

\paragraph{Mean Squared Error (MSE):}
\begin{equation}
    \text{MSE} = \frac{1}{N} \sum_{i=1}^{N} (X_i - \hat{X}_i)^2
\end{equation}
This metric measures the average squared differences between the predicted and actual values.

\paragraph{Mean Absolute Error (MAE):}
\begin{equation}
    \text{MAE} = \frac{1}{N} \sum_{i=1}^{N} |X_i - \hat{X}_i|
\end{equation}
MAE provides an average magnitude of errors in the predictions without considering their direction.
The experiments are conducted using two NVIDIA RTX 3090 GPUs, with PyTorch 3.8 on an Ubuntu 14.04 system.

\subsection{Baseline Models}
To ensure a fair comparison, we included a diverse set of baseline models spanning general-purpose time series architectures, forecasting-focused models, and state-of-the-art imputation techniques.For general-purpose tasks, we selected ModernTCN \cite{ModerTcn} and TimesNet \cite{timesnet}, which address various time series challenges, including forecasting and imputation. Forecasting models often treat imputation as an extension of short-term prediction. This category includes Transformer-based architectures like Crossformer \cite{Crossformer}, FEDformer \cite{fedformer}, and PatchTST \cite{PatchTST}, which leverage self-attention for long-term dependencies, as well as MLP-based models such as LightTS \cite{LightTS2023}, DLinear \cite{DLinear_AAAI2023}, and RLinear \cite{Rlinear}, which use efficient linear mappings.For imputation-specific tasks, we evaluated BRITS \cite{brits}, SAITS \cite{DU}, and Transformer-based models designed to learn temporal and variable-level dependencies in incomplete time series. Additionally, CNN-based methods like MICN \cite{micn} and SCINet \cite{liu2022SCINet} were included for their efficiency in capturing local temporal features. This diverse selection of baselines ensures a rigorous and balanced evaluation of PGConvNet across multiple time series modeling paradigms.


\subsection{Comparison Experiments}
To ensure a fair comparison, we maintain consistent experimental settings with the baseline studies. For each dataset, we use a sequence length of 96 and randomly mask values at different missing rates \{12.5\%, 25\%, 37.5\%, 50\%\} to simulate various levels of missingness. The models are then tasked with imputing these missing values, and their performance is evaluated using MSE and MAE. In the results presented in Table~\ref{tab:comparison_table}, the best-performing models are marked in red, while the second-best results are highlighted in blue. 
\FloatBarrier
\renewcommand{\arraystretch}{1.2}  % 增加行高比例
\begin{table*}[htbp]
    \centering
    \caption{Results from the comparative experiments, where different models are evaluated under varying missing data rates (12.5\%, 25\%, 37.5\%, and 50\%) by randomly masking time points.}
    \label{tab:comparison_table}
    \resizebox{\textwidth}{!}{%
    \begin{tabular}{>{\centering\arraybackslash}m{1cm}|c|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc|cc}
        \toprule
        \multirow{3}{*}{Data} & \multirow{3}{*}{Ratio} & \multicolumn{2}{c}{PGConvNet} & \multicolumn{2}{c}{ModernTCN} & \multicolumn{2}{c}{PatchTST} & \multicolumn{2}{c}{Crossformer} & \multicolumn{2}{c}{FEDformer} & \multicolumn{2}{c}{MTS-mixer} & \multicolumn{2}{c}{LightTS} & \multicolumn{2}{c}{DLinear} & \multicolumn{2}{c}{TimesNet} & \multicolumn{2}{c}{MICN} & \multicolumn{2}{c}{SCINet} & \multicolumn{2}{c}{RLinear} & \multicolumn{2}{c}{BRITS} & \multicolumn{2}{c}{SAITS} & \multicolumn{2}{c}{Transformer}\\
        & & \multicolumn{2}{c}{(Ours)} & \multicolumn{2}{c}{2024ICLR} & \multicolumn{2}{c}{2023ICLR} & \multicolumn{2}{c}{2023ICLR} & \multicolumn{2}{c}{2022ICML} & \multicolumn{2}{c}{2023ICML} & \multicolumn{2}{c}{2023NeurIPS} & \multicolumn{2}{c}{2023AAAI} & \multicolumn{2}{c}{2023ICLR} & \multicolumn{2}{c}{2023ICLR} & \multicolumn{2}{c}{2022NeurIPS} & \multicolumn{2}{c}{2023ArXiv} & \multicolumn{2}{c}{2018NeurIPS} & \multicolumn{2}{c}{2023ESWA} & \multicolumn{2}{c}{2017NeurIPS}\\
        \cmidrule(lr){3-32}
        & & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE & MSE & MAE\\
        \midrule
        \multirow{5}{*}{\rotatebox{90}{ETTm1}} 
        & 12.5\% & \textcolor{blue}{0.014} & \textcolor{blue}{0.078} & 0.015 & 0.082 & 0.041 & 0.128 & 0.037 & 0.137 & 0.035 & 0.135 & 0.043 & 0.134 & 0.075 & 0.180 & 0.058 & 0.162 & 0.019 & 0.092 & 0.039 & 0.137 & 0.031 & 0.116 & 0.047 & 0.137 & 0.034 & 0.107 & \textcolor{red}{0.013} & \textcolor{red}{0.075}& 0.022 & 0.102 \\
        & 25\% & \textcolor{blue}{0.016} & \textcolor{blue}{0.083} & 0.018 & 0.088 & 0.043 & 0.130 & 0.038 & 0.141 & 0.052 & 0.166 & 0.051 & 0.147 & 0.093 & 0.206 & 0.080 & 0.193 & 0.023 & 0.101 & 0.059 & 0.170 & 0.036 & 0.124 & 0.061 & 0.157 & 0.047 & 0.124 & \textcolor{red}{0.016} & \textcolor{red}{0.081}& 0.029 & 0.119 \\
        & 37.5\% & \textcolor{blue}{0.019} & \textcolor{red}{0.089} & 0.021 & 0.095 & 0.044 & 0.133 & 0.041 & 0.142 & 0.069 & 0.191 & 0.060 & 0.160 & 0.113 & 0.231 & 0.103 & 0.219 & 0.029 & 0.111 & 0.080 & 0.199 & 0.041 & 0.134 & 0.077 & 0.175 & 0.056 & 0.138 & \textcolor{red}{0.019} & \textcolor{blue}{0.098} & 0.037 & 0.135\\
        & 50\% & \textcolor{blue}{0.023} & \textcolor{red}{0.098} & 0.026 & 0.105 & 0.050 & 0.142 & 0.047 & 0.152 & 0.089 & 0.218 & 0.070 & 0.174 & 0.134 & 0.255 & 0.132 & 0.248 & 0.036 & 0.124 & 0.103 & 0.221 & 0.049 & 0.143 & 0.096 & 0.195 & 0.072 & 0.158 & \textcolor{red}{0.023} & \textcolor{blue}{0.100}& 0.045 & 0.148 \\
        \cmidrule(lr){2-32}
        & Avg & \textcolor{red}{0.018} & \textcolor{red}{0.087} & \textcolor{blue}{0.020} & \textcolor{blue}{0.093} & 0.045 & 0.133 & 0.041 & 0.143 & 0.062 & 0.177 & 0.056 & 0.154 & 0.104 & 0.218 & 0.093 & 0.206 & 0.027 & 0.107 & 0.070 & 0.182 & 0.039 & 0.129 & 0.070 & 0.166 & 0.052 & 0.132 & \textcolor{blue}{0.018} & \textcolor{blue}{0.089}& 0.033 & 0.126  \\
        \midrule
        
        
        \multirow{5}{*}{\rotatebox{90}{ETTm2}} 
        & 12.5\% & \textcolor{red}{0.015} & \textcolor{red}{0.068} & \textcolor{blue}{0.017} & \textcolor{blue}{0.076} & 0.025 & 0.092 & 0.044 & 0.148 & 0.056 & 0.159 & 0.026 & 0.096 & 0.034 & 0.127 & 0.062 & 0.166 & 0.018 & 0.080 & 0.060 & 0.165 & 0.023 & 0.093 & 0.026 & 0.093 & 0.024 & 0.091 & 0.019 & 0.085 & 0.170 & 0.298\\
        
        & 25\% & \textcolor{red}{0.017} & \textcolor{red}{0.075} & \textcolor{blue}{0.018} & \textcolor{blue}{0.080} & 0.027 & 0.095 & 0.047 & 0.151 & 0.080 & 0.195 & 0.030 & 0.103 & 0.042 & 0.143 & 0.085 & 0.196 & 0.020 & 0.085 & 0.100 & 0.216 & 0.026 & 0.100 & 0.030 & 0.103 & 0.028 & 0.100 & 0.022 & 0.094 & 0.210 & 0.334\\
        
        & 37.5\% & \textcolor{red}{0.018} & \textcolor{red}{0.078} & \textcolor{blue}{0.020} & \textcolor{blue}{0.084} & 0.029 & 0.099 & 0.044 & 0.145 & 0.110 & 0.231 & 0.033 & 0.110 & 0.051 & 0.159 & 0.106 & 0.222 & 0.023 & 0.091 & 0.163 & 0.273 & 0.028 & 0.105 & 0.034 & 0.113 & 0.036 & 0.116 & 0.028 & 0.109 & 0.240 & 0.359 \\
        
        & 50\% & \textcolor{red}{0.021} & \textcolor{red}{0.084} & \textcolor{blue}{0.022} & \textcolor{blue}{0.090} & 0.032 & 0.106 & 0.047 & 0.150 & 0.156 & 0.276 & 0.037 & 0.118 & 0.059 & 0.147 & 0.131 & 0.247 & 0.026 & 0.098 & 0.254 & 0.342 & 0.031 & 0.111 & 0.039 & 0.123 & 0.043 & 0.127 & 0.039 & 0.130 & 0.247 & 0.384 \\
        
        \cmidrule(lr){2-32}
        & Avg & \textcolor{red}{0.018} & \textcolor{red}{0.076} & \textcolor{blue}{0.019} & \textcolor{blue}{0.082} & 0.028 & 0.098 & 0.046 & 0.149 & 0.101 & 0.215 & 0.032 & 0.107 & 0.046 & 0.151 & 0.096 & 0.208 & 0.022 & 0.088 & 0.144 & 0.249 & 0.027 & 0.102 & 0.032 & 0.108 & 0.033 & 0.108 & 0.027 & 0.105 & 0.217 & 0.344 \\
        \midrule
        \multirow{5}{*}{\rotatebox{90}{ETTh1}} 
        & 12.5\% & \textcolor{blue}{0.029} & \textcolor{blue}{0.115} & 0.035 & 0.128 & 0.049 & 0.199 & 0.099 & 0.218 & 0.070 & 0.190 & 0.097 & 0.209 & 0.240 & 0.345 & 0.151 & 0.267 & 0.057 & 0.159 & 0.072 & 0.192 & 0.089 & 0.202 & 0.098 & 0.206 & 0.072 & 0.161 & \textcolor{red}{0.027} & \textcolor{red}{0.106}& 0.065 & 0.180  \\

        & 25\% & \textcolor{blue}{0.036} & \textcolor{blue}{0.128} & 0.042& 0.140 & 0.119 & 0.225 & 0.125 & 0.243 & 0.106 & 0.236 & 0.115 & 0.226 & 0.265 & 0.364 & 0.180 & 0.292 & 0.069 & 0.178 & 0.105 & 0.232 & 0.099 & 0.211 & 0.123 & 0.229 & 0.097 & 0.191 & \textcolor{red}{0.034} & \textcolor{red}{0.123} & 0.087 & 0.210 \\

        & 37.5\% & \textcolor{red}{0.046} & \textcolor{red}{0.144} & 0.054 & 0.157 & 0.145 & 0.248 & 0.146 & 0.263 & 0.124 & 0.258 & 0.135 & 0.244 & 0.296 & 0.382 & 0.215 & 0.318 & 0.084 & 0.196 & 0.139 & 0.267 & 0.107 & 0.218 & 0.153 & 0.253 & 0.124 & 0.222 & \textcolor{blue}{0.051} & \textcolor{blue}{0.149} & 0.112 & 0.240\\

        & 50\% & \textcolor{red}{0.058} & \textcolor{red}{0.161} &0.067 & 0.174 & 0.173 & 0.271 & 0.158 & 0.281 & 0.165 & 0.299 & 0.160 & 0.263 & 0.334 & 0.404 & 0.257 & 0.347 & 0.102 & 0.215 & 0.185 & 0.310 & 0.120 & 0.231 & 0.188 & 0.278 & 0.160 & 0.253 & \textcolor{blue}{0.064} & \textcolor{blue}{0.169} & 0.136 & 0.264\\
        \cmidrule(lr){2-32}
        & Avg & \textcolor{red}{0.042} & \textcolor{red}{0.137} & 0.050& 0.150 & 0.133 & 0.236 & 0.132 & 0.251 & 0.117 & 0.246 & 0.127 & 0.236 & 0.284 & 0.373 & 0.201 & 0.306 & 0.078 & 0.187 & 0.125 & 0.250 & 0.104 & 0.216 & 0.141 & 0.242 & 0.113 & 0.207 & \textcolor{blue}{0.044} & \textcolor{blue}{0.137} & 0.101 & 0.224 \\
        \midrule
        \multirow{5}{*}{\rotatebox{90}{ETTh2}} 
        & 12.5\% & \textcolor{red}{0.032} & \textcolor{red}{0.110} & \textcolor{blue}{0.037} & \textcolor{blue}{0.121} & 0.057 & 0.150 & 0.103 & 0.220 & 0.095 & 0.212 & 0.061 & 0.157 & 0.101 & 0.231 & 0.100 & 0.216 & 0.040 & 0.130 & 0.106 & 0.223 & 0.061 & 0.161 & 0.057 & 0.152 & 0.035 & 0.120 & 0.056 & 0.150 & 0.177 & 0.319  \\
        & 25\% & \textcolor{red}{0.036} & \textcolor{red}{0.118} & \textcolor{blue}{0.040} & \textcolor{blue}{0.127} & 0.062 & 0.158 & 0.110 & 0.229 & 0.137 & 0.258 & 0.065 & 0.163 & 0.115 & 0.246 & 0.127 & 0.247 & 0.046 & 0.141 & 0.151 & 0.271 & 0.062 & 0.162 & 0.062 & 0.160 & 0.043 & 0.137 & 0.075 & 0.179 & 0.266 & 0.387 \\
        
        & 37.5\% & \textcolor{red}{0.039} & \textcolor{red}{0.124} & \textcolor{blue}{0.043} & \textcolor{blue}{0.134} & 0.068 & 0.168 & 0.129 & 0.246 & 0.187 & 0.304 & 0.070 & 0.171 & 0.126 & 0.257 & 0.158 & 0.276 & 0.052 & 0.151 & 0.229 & 0.332 & 0.065 & 0.166 & 0.068 & 0.168 & 0.053 & 0.153 & 0.087 & 0.199 & 0.303 & 0.404 \\
        & 50\% & \textcolor{red}{0.045} & \textcolor{red}{0.136} & \textcolor{blue}{0.048} & \textcolor{blue}{0.143} & 0.076 & 0.179 & 0.148 & 0.265 & 0.232 & 0.341 & 0.078 & 0.181 & 0.136 & 0.268 & 0.183 & 0.299 & 0.060 & 0.162 & 0.334 & 0.403 & 0.069 & 0.172 & 0.076 & 0.179 & 0.073 & 0.186 & 0.092 & 0.205 & 0.343 & 0.428 \\
        \cmidrule(lr){2-32}
        & Avg & \textcolor{red}{0.038} & \textcolor{red}{0.122} & \textcolor{blue}{0.042} & \textcolor{blue}{0.131} & 0.066 & 0.164 & 0.122 & 0.240 & 0.163 & 0.279 & 0.069 & 0.168 & 0.119 & 0.250 & 0.142 & 0.259 & 0.049 & 0.146 & 0.205 & 0.307 & 0.064 & 0.165 & 0.066 & 0.165 & 0.051 & 0.149 & 0.078 & 0.183 & 0.272 & 0.385  \\
        
        \midrule
        \multirow{5}{*}{\rotatebox{90}{Electricity}} 
        & 12.5\% & \textcolor{red}{0.046} & \textcolor{red}{0.145} & \textcolor{blue}{0.059} & \textcolor{blue}{0.171} & 0.073 & 0.188 & 0.068 & 0.181 & 0.107 & 0.237 & 0.069 & 0.182 & 0.102 & 0.229 & 0.092 & 0.214 & 0.085 & 0.202 & 0.090 & 0.216 & 0.073 & 0.185 & 0.079 & 0.199 & 0.171 & 0.299 & 0.131 & 0.256 & 0.149 & 0.277 \\
        & 25\% & \textcolor{red}{0.053} & \textcolor{red}{0.155} & \textcolor{blue}{0.071} & \textcolor{blue}{0.188} & 0.082 & 0.200 & 0.079 & 0.198 & 0.120 & 0.251 & 0.083 & 0.202 & 0.121 & 0.252 & 0.118 & 0.247 & 0.089 & 0.206 & 0.108 & 0.236 & 0.081 & 0.198 & 0.105 & 0.233 & 0.175 & 0.302 & 0.147 & 0.270 & 0.161 & 0.284 \\
        & 37.5\% & \textcolor{red}{0.061} & \textcolor{red}{0.167} & \textcolor{blue}{0.077} & \textcolor{blue}{0.190} & 0.097 & 0.217 & 0.087 & 0.203 & 0.136 & 0.266 & 0.097 & 0.218 & 0.141 & 0.273 & 0.144 & 0.276 & 0.094 & 0.213 & 0.128 & 0.257 & 0.090 & 0.207 & 0.131 & 0.262 & 0.180 & 0.304 & 0.161 & 0.282  & 0.169 & 0.292 \\
        & 50\% & \textcolor{red}{0.068} & \textcolor{red}{0.175} & \textcolor{blue}{0.085} & \textcolor{blue}{0.200} & 0.110 & 0.232 & 0.096 & 0.212 & 0.158 & 0.284 & 0.108 & 0.231 & 0.160 & 0.293 & 0.175 & 0.305 & 0.100 & 0.221 & 0.151 & 0.278 & 0.099 & 0.214 & 0.160 & 0.291 & 0.186 & 0.307 & 0.180 & 0.292 & 0.178 & 0.296\\
        \cmidrule(lr){2-32}
        & Avg & \textcolor{red}{0.057} & \textcolor{red}{0.160} & \textcolor{blue}{0.073} & \textcolor{blue}{0.187} & 0.091 & 0.209 & 0.083 & 0.199 & 0.130 & 0.259 & 0.089 & 0.208 & 0.131 & 0.262 & 0.132 & 0.260 & 0.092 & 0.210 & 0.119 & 0.247 & 0.086 & 0.201 & 0.119 & 0.246 & 0.178 & 0.303 & 0.155 & 0.275 & 0.164 & 0.287\\
        
        
        \midrule
        \multirow{5}{*}{\rotatebox{90}{Weather}} 
        & 12.5\% & \textcolor{red}{0.023} & \textcolor{red}{0.038} & \textcolor{blue}{0.023} & \textcolor{blue}{0.039} & 0.029 & 0.049 & 0.036 & 0.092 & 0.041 & 0.107 & 0.033 & 0.052 & 0.047 & 0.101 & 0.039 & 0.084 & 0.025 & 0.045 & 0.036 & 0.088 & 0.028 & 0.047 & 0.029 & 0.048 & 0.041 & 0.075 & 0.029 & 0.062 & 0.031 & 0.078 \\
        
        & 25\% & \textcolor{red}{0.024} & \textcolor{red}{0.040} & \textcolor{blue}{0.025} & \textcolor{blue}{0.041} & 0.031 & 0.053 & 0.035 & 0.088 & 0.064 & 0.163 & 0.034 & 0.056 & 0.052 & 0.111 & 0.048 & 0.103 & 0.029 & 0.052 & 0.047 & 0.115 & 0.029 & 0.050 & 0.032 & 0.055 & 0.042 & 0.075 & 0.030 & 0.064 & 0.038 & 0.097  \\
        
        & 37.5\% & \textcolor{red}{0.027} & \textcolor{red}{0.046} & \textcolor{blue}{0.029} & \textcolor{blue}{0.049} & 0.034 & 0.058 & 0.035 & 0.088 & 0.107 & 0.229 & 0.037 & 0.060 & 0.058 & 0.121 & 0.057 & 0.117 & 0.031 & 0.057 & 0.062 & 0.141 & 0.031 & 0.055 & 0.036 & 0.062 & 0.048 & 0.087 & 0.033 & 0.068 & 0.039 & 0.099 \\
        
        & 50\% & \textcolor{red}{0.030} & \textcolor{red}{0.050} & \textcolor{blue}{0.031} & \textcolor{blue}{0.051} & 0.039 & 0.066 & 0.038 & 0.092 & 0.183 & 0.312 & 0.041 & 0.066 & 0.065 & 0.133 & 0.066 & 0.134 & 0.034 & 0.062 & 0.080 & 0.168 & 0.034 & 0.059 & 0.040 & 0.067 & 0.051 & 0.089 & 0.036 & 0.074  & 0.048 & 0.113\\
        
        \cmidrule(lr){2-32}
        & Avg & \textcolor{red}{0.026} & \textcolor{red}{0.043} & \textcolor{blue}{0.027} & \textcolor{blue}{0.044} & 0.033 & 0.057 & 0.036 & 0.090 & 0.099 & 0.203 & 0.036 & 0.058 & 0.055 & 0.117 & 0.052 & 0.110 & 0.030 & 0.054 & 0.056 & 0.128 & 0.031 & 0.053 & 0.034 & 0.058 & 0.045 & 0.082 & 0.032 & 0.067  & 0.039 & 0.097\\
        \midrule
        \multicolumn{2}{l|}{1$^{st}$ Count} & 50 &  & 0 &  & 0 &  & 0 &  & 0 &  & 0 &  & 0 &  & 0 &  & 0 &  & 0 &  & 0 & & 0 & & 0 & & 10 & & 0 & \\
        \bottomrule
    \end{tabular}%
    }

\end{table*}



\FloatBarrier
\begin{figure*}[htbp]
    \centering
    \includegraphics[height=0.64\textwidth,width=1\textwidth]{figure5.png}  % Adjust the image path to your image file  
 \caption{Comparison of MSE across six datasets under varying missing data rates (12.5\%, 25\%, 37.5\%, 50\%). The plot illustrates PGConvNet's superior performance with lower MSE.}
    \label{fig:mse_comparison}
\end{figure*}

\FloatBarrier
\begin{figure*}[htbp]
    \centering
     \includegraphics[height=0.64\textwidth,width=1\textwidth]{figure6.png}  % Adjust the image path to your image file
   \caption{Comparison of MAE across six datasets under varying missing data rates (12.5\%, 25\%, 37.5\%, 50\%). The plot highlights PGConvNet’s superior accuracy in terms of lower MAE, showing its capacity to adapt to different levels of data incompleteness.}
    \label{fig:mae_comparison}
\end{figure*}


The results from our comparative experiments, summarized in Table~\ref{tab:comparison_table} and visually supported by Figures \ref{fig:mse_comparison} and \ref{fig:mae_comparison}, demonstrate the strong performance of PGConvNet. PGConvNet achieves competitive MSE and MAE metrics across multiple datasets and missing data rates (12.5\%, 25\%, 37.5\%, and 50\%).

Starting with RNN-based models, BRITS captures sequential dependencies effectively due to its recurrent structure. However, its reliance on sequential processing limits its ability to generalize across datasets with diverse temporal granularities, particularly at higher missing rates. For instance, in the ETTh1 dataset at a 50\% missing rate, BRITS records an MSE that is \textbf{260\% }higher than PGConvNet, highlighting the latter’s superior handling of random missing data.For CNN-based models, such as ModernTCN and TimesNet, these methods excel at capturing local temporal dependencies but struggle with cross-variable interactions as the missing rate increases. In the ETTm1 dataset at a 37.5\% missing rate, PGConvNet achieves an \textbf{34\%  }lower MSE compared to TimesNet. Additionally, PGConvNet demonstrates its robustness across datasets with varying temporal granularities, achieving an average MSE reduction of \textbf{25\%} over CNN-based models across all datasets.Moving to MLP-based models, such as DLinear and LightTS, these architectures rely on simplified linear or lightweight transformations, which enable faster runtimes and lower memory consumption. However, their minimal structural complexity hinders their ability to model intricate temporal patterns effectively, especially as the missing rate increases. For example, in the Electricity dataset at a 25\% missing rate, PGConvNet achieves an \textbf{55\%  }lower MSE compared to DLinear, demonstrating its superior capability to capture complex temporal and cross-variable dependencies.Transformer-based models, including PatchTST, Crossformer, and FEDformer, exhibit robust sequence modeling capabilities at lower missing rates but face a \textbf{30-40\% }increase in error at higher missing rates due to their computational inefficiencies in handling random missing data. Notably, SAITS demonstrates strong performance in datasets like ETTh1 and ETTm1, where it achieves an MSE that is \textbf{5-10\% }lower than PGConvNet, driven by its diagonal masking self-attention (DMSA) block design. These results highlight areas where PGConvNet could improve, particularly in capturing critical relationships between key temporal points and variable interactions in specific datasets.


%接下来的就是消融实验部分
\subsection{Ablation Study}

To evaluate the contributions of individual components in PGConvNet, we performed an ablation study focusing on the MSGBlock and PGCBlock. The results, shown in Figure~\ref{fig:ablation_study_mse}, utilize MSE values to illustrate the performance impact of removing each block independently. 
\begin{figure}[htbp]
\centering
\includegraphics[width=\textwidth]{figure7.png}
\caption{Ablation study results for MSE across different datasets comparing \textit{Without PGCBlock}, \textit{Without MSGBlock}, and the \textit{Full PGConvNet}.}
\label{fig:ablation_study_mse}
\end{figure}

The MSGBlock specializes in extracting temporal and variable interdependencies but lacks the flexibility to adapt dynamically to missing data. This limitation is particularly evident in datasets like ETTh1, where intricate temporal patterns and variable relationships require more adaptive mechanisms. In contrast, the PGCBlock excels at dynamically adjusting to missing values by leveraging parametric convolutions to adapt to their random placement. However, without the structured feature extraction provided by the MSGBlock, the PGCBlock struggles with capturing broader temporal and variable interdependencies, as observed in datasets like ECL and Weather.

The integration of both modules in the full PGConvNet mitigates these individual shortcomings, offering a robust solution that balances temporal modeling and dynamic adaptation. For example, in the ETTh1 dataset, the full PGConvNet achieves a substantial improvement, reducing MSE by approximately 20\% compared to using either module independently. Similarly, on the ETTm2 dataset, the combined architecture effectively captures temporal dependencies while addressing missing values, leading to more consistent and accurate imputation.

These results highlight the complementary nature of the MSGBlock and PGCBlock. By combining structured feature extraction with dynamic adaptation, PGConvNet delivers significant advantages in handling missing data and modeling complex temporal relationships. However, we acknowledge that certain datasets, such as ECL, reveal outliers in performance when individual blocks are used. This suggests opportunities for further refinement, particularly in enhancing the robustness of temporal and variable modeling under diverse conditions.



\subsection{Training Time and Memory Usage Analysis}

To evaluate the efficiency and computational overhead of PGConvNet, we conducted experiments comparing its training time and memory usage against several other baseline models. Table~\ref{tab:performance_comparison} summarizes the results on the ETTh1 dataset under a missing rate of 50\%, highlighting the trade-offs between model complexity, runtime, and memory consumption.

\begin{table*}[htbp]
\centering
\caption{Performance comparison of PGConvNet and baseline models on the ETTh1 dataset (50\% missing rate), including memory usage, runtime, and MSE.}
\label{tab:performance_comparison}
\renewcommand{\arraystretch}{1.2}
\setlength{\tabcolsep}{6pt}
\resizebox{\textwidth}{!}{%
\begin{tabular}{l|c|c|c|c|c|c|c|c|c|c|c|}
\toprule
\textbf{Model} & \textbf{PGConvNet} & \textbf{PGCBlock} & \textbf{TimesNet} & \textbf{Autoformer} & \textbf{MICN} & \textbf{DLinear} & \textbf{BRITS} & \textbf{SAITS} & \textbf{Reformer} & \textbf{Pyraformer} & \textbf{FEDformer} \\
\midrule
\textbf{Memory Usage (MB)} & \textbf{542} & \textbf{418} & \textbf{386} & \textbf{586} & \textbf{636} & \textbf{336} & \textbf{344} & \textbf{448} & \textbf{582} & \textbf{446} & \textbf{578} \\
\textbf{Runtime (s)} & \textbf{7.04} & \textbf{4.94} & \textbf{11.79} & \textbf{8.71} & \textbf{5.36} & \textbf{2.48} & \textbf{66.74} & \textbf{6.43} & \textbf{7.19} & \textbf{14.46} & \textbf{47.88} \\
\textbf{MSE} & \textbf{0.058} & \textbf{0.131} & \textbf{0.102} & \textbf{0.137} & \textbf{0.185} & \textbf{0.257} & \textbf{0.160} & \textbf{0.065} & \textbf{0.143} & \textbf{0.162} & \textbf{0.199} \\
\bottomrule
\end{tabular}%
}
\end{table*}

The results in Table~\ref{tab:performance_comparison} demonstrate that PGConvNet achieves a promising balance between computational efficiency and imputation performance. This success is attributed to its hybrid design, integrating both 1D and 2D convolutions, which enables effective modeling of temporal dependencies while maintaining moderate resource usage. In comparison, MLP-based models like DLinear, with their simple design, are efficient in memory and runtime but fail to capture complex temporal patterns, reducing imputation accuracy. Other CNN-based models enhance modeling capacity but struggle to balance runtime and memory efficiency effectively.

Furthermore, we evaluated the standalone performance of the PGCBlock module to understand its contribution to PGConvNet. The results confirm that PGCBlock outperforms most Transformer-based models in terms of computational efficiency and imputation accuracy. This improvement is largely due to its use of parametric convolutions that dynamically adapt to the random positioning of missing values, allowing for more effective and context-aware modeling. At the same time, we must acknowledge that SAITS also performs exceptionally well, demonstrating strong accuracy in capturing complex temporal dependencies.

In conclusion, PGConvNet demonstrates competitive performance in imputation accuracy while maintaining a well-balanced trade-off between memory usage and runtime. This balance makes it a practical solution for time series imputation tasks where both efficiency and effectiveness are essential.





\subsection{Hyperparameter Analysis}

In the previous sections, we conducted comprehensive benchmark experiments, ablation studies, and runtime evaluations to evaluate the performance of PGConvNet. Building upon these findings, this section systematically investigates the influence of key hyperparameters—such as the feature channel dimension ($D$), multi-scale convolutional group configuration, and the number of MSGBlock layers ($k$)—on PGConvNet's performance across diverse datasets. Specifically, we aim to elucidate how these hyperparameters affect data fidelity and generalization, particularly under different levels of data sparsity.


\begin{figure}[htbp]
    \centering
    \includegraphics[width=1\textwidth]{figure8.png}
    \caption{Impact of Feature Channel Dimension ($D$) on Model Performance.  Increasing $D$ generally improves the model's ability to capture temporal features, with $D=128$ achieving the best performance.  However, further increases lead to diminishing returns, particularly under larger missing ratios, due to overfitting and computational overhead.}
    \label{fig:feature_channel_dimension}
\end{figure}


\begin{figure}[htbp]
    \centering
    \includegraphics[width=1\textwidth]{figure9.png}
    \caption{Effect of MSGBlock Kernel Sizes. Larger kernel sizes capture long-term dependencies, while smaller kernels suit high temporal resolution datasets. Among the tested configurations, the kernel size of (3, 1) demonstrates the best overall performance.}
    \label{fig:kernel_size_effect}
\end{figure}

\begin{figure}[htbp]
    \centering
    \includegraphics[width=1\textwidth]{figure10.png}
    \caption{Effect of MSGBlock Layers ($k$) on MSE. Increasing layers improves performance initially but leads to diminishing returns beyond $k=3$. The optimal number of layers depends on the dataset and sparsity level.}
    \label{fig:msgblock_layers_effect}
\end{figure}

Figures~\ref{fig:feature_channel_dimension} to \ref{fig:msgblock_layers_effect} comprehensively depict the impact of critical hyperparameters on PGConvNet's performance across diverse datasets. The feature channel dimension ($D$) emerges as a pivotal factor influencing the model's capacity to capture temporal dependencies. An increase in $D$ generally enhances the representation power, particularly under scenarios with lower missing ratios. However, as the missing ratio escalates, the incremental benefits diminish, signaling the necessity of striking a balance between model complexity and the risk of overfitting.Similarly, kernel size within the MSGBlock configuration plays a decisive role. While larger kernels are adept at capturing long-term temporal dependencies, smaller kernels prove more effective for datasets characterized by high temporal resolution. This underscores the importance of tailoring kernel size to align with the temporal characteristics of the dataset to ensure optimal performance.The number of MSGBlock layers ($k$) also exerts a significant influence. Increasing $k$ initially leads to notable performance gains by improving MSE; however, diminishing returns are observed beyond $k=2$. This highlights the importance of avoiding excessive layering, which may not translate into proportional performance improvements.
To maintain fairness and consistency, identical hyperparameter configurations were employed across all datasets in the benchmark experiments. This rigorous standardization ensures that the observed variations in performance are exclusively attributable to the intrinsic characteristics of the datasets and hyperparameter choices, rather than discrepancies in experimental design. For additional implementation details and a comprehensive discussion of the results, please refer to the Appendix.

\section{Conclusion and Future Work}

In this work, we present PGConvNet, an innovative framework for efficient time series imputation that addresses the limitations of conventional MLP-based and Transformer-based methodologies. By leveraging a hybrid convolutional architecture, PGConvNet effectively balances computational efficiency with modeling capability, capturing intricate temporal dynamics and cross-variable dependencies through its two-stage design comprising the MSGBlock and the PGCBlock. This architecture is particularly adept at addressing the challenges posed by random missing values, a critical bottleneck in multivariate time series analysis.

Comprehensive evaluations across six diverse benchmark datasets demonstrate that, in most scenarios, PGConvNet outperforms state-of-the-art models in terms of MSE and MAE metrics, particularly under high missing data rates.The results are further corroborated by ablation studies, which validate the importance of multi-scale feature extraction and the dynamic adaptability of the proposed architecture. These findings highlight PGConvNet’s potential to serve as a robust solution for handling data incompleteness in real-world applications.

Nevertheless, we acknowledge that PGConvNet's computational complexity may escalate in scenarios involving extremely high-dimensional datasets, which could pose challenges for scalability in resource-constrained environments. Future research directions could focus on refining the computational efficiency of the multi-scale convolutional modules, leveraging sparsity-aware techniques, and incorporating domain-specific inductive biases to enhance the model's generalization capabilities. Moreover, extending PGConvNet to allied tasks such as time series forecasting, anomaly detection, and causality inference represents a promising avenue for further exploration.








\section*{Declaration of Competing Interest}

The authors declare that they have no known competing financial interests or personal relationships that could have appeared to influence the work reported in this paper.

\section*{Data Availability}

The datasets analyzed during the current study are available within the manuscript. All relevant data, including experimental results, are fully detailed in the Experimental Section and tables to ensure complete transparency and reproducibility.





\section*{My Appendix}
\renewcommand{\thetable}{A\arabic{table}} % Change table numbering to A1, A2, etc.
\setcounter{table}{0} % Reset table counter
\begin{table*}[htbp]
\centering
\caption{Performance Comparison for Different Dimensionalities (D=32, 64, 128, 256)}
\renewcommand{\arraystretch}{1.2} % Adjust row height for better readability
\setlength{\tabcolsep}{4pt} % Adjust column spacing
\resizebox{1\linewidth}{!}{
\begin{tabular}{c|c|cc|cc|cc|cc}
\toprule
\multirow{2}{*}{\textbf{Data}} & \multirow{2}{*}{\textbf{Ratio}} & \multicolumn{2}{c|}{\textbf{Dim=32}} & \multicolumn{2}{c|}{\textbf{Dim=64}} & \multicolumn{2}{c|}{\textbf{\underline{Dim=128}}} & \multicolumn{2}{c}{\textbf{Dim=256}} \\
 & & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} \\
\midrule
\multirow{4}{*}{ETTm1} 
& 12.5\%  & 0.015 & 0.082 & 0.015 & 0.080 & \underline{0.014} & \underline{0.078} & 0.016 & 0.084 \\
& 25\%    & 0.017 & 0.087 & 0.017 & 0.085 & \underline{0.016} & \underline{0.083} & 0.018 & 0.090 \\
& 37.5\%  & 0.020 & 0.094 & 0.019 & 0.091 & \underline{0.019} & \underline{0.089} & 0.021 & 0.096 \\
& 50\%    & 0.025 & 0.106 & 0.024 & 0.102 & \underline{0.023} & \underline{0.098} & 0.026 & 0.107 \\
\midrule
\multirow{4}{*}{ETTm2} 
& 12.5\%  & 0.017 & 0.074 & 0.016 & 0.072 & \underline{0.015} & \underline{0.068} & 0.016 & 0.071 \\
& 25\%    & 0.018 & 0.079 & 0.017 & 0.075 & \underline{0.017} & \underline{0.075} & 0.017 & 0.074 \\
& 37.5\%  & 0.021 & 0.085 & 0.019 & 0.080 & \underline{0.018} & \underline{0.078} & 0.019 & 0.080 \\
& 50\%    & 0.023 & 0.090 & 0.022 & 0.087 & \underline{0.021} & \underline{0.084} & 0.022 & 0.086 \\
\midrule
\multirow{4}{*}{ETTh1} 
& 12.5\%  & 0.034 & 0.126 & 0.029 & 0.117 & \underline{0.029} & \underline{0.115} & 0.031 & 0.119 \\
& 25\%    & 0.043 & 0.140 & 0.039 & 0.133 & \underline{0.036} & \underline{0.128} & 0.038 & 0.132 \\
& 37.5\%  & 0.053 & 0.156 & 0.049 & 0.148 & \underline{0.046} & \underline{0.144} & 0.048 & 0.147 \\
& 50\%    & 0.071 & 0.180 & 0.062 & 0.168 & \underline{0.058} & \underline{0.161} & 0.061 & 0.165 \\
\midrule
\multirow{4}{*}{ETTh2} 
& 12.5\%  & 0.035 & 0.116 & 0.034 & 0.114 & \underline{0.032} & \underline{0.110} & 0.033 & 0.110 \\
& 25\%    & 0.038 & 0.124 & 0.037 & 0.120 & \underline{0.036} & \underline{0.118} & 0.035 & 0.116 \\
& 37.5\%  & 0.042 & 0.133 & 0.041 & 0.129 & \underline{0.039} & \underline{0.124} & 0.040 & 0.125 \\
& 50\%    & 0.048 & 0.144 & 0.048 & 0.142 & \underline{0.045} & \underline{0.136} & 0.045 & 0.136 \\
\midrule
\multirow{4}{*}{Electricity} 
& 12.5\%  & 0.042 & 0.135 & 0.043 & 0.138 & \underline{0.046} & \underline{0.145} & 0.053 & 0.158 \\
& 25\%    & 0.047 & 0.144 & 0.049 & 0.146 & \underline{0.053} & \underline{0.155} & 0.062 & 0.172 \\
& 37.5\%  & 0.054 & 0.154 & 0.055 & 0.153 & \underline{0.061} & \underline{0.167} & 0.073 & 0.184 \\
& 50\%    & 0.059 & 0.162 & 0.061 & 0.163 & \underline{0.068} & \underline{0.175} & 0.080 & 0.194 \\
\midrule
\multirow{4}{*}{Weather} 
& 12.5\%  & 0.025 & 0.043 & 0.027 & 0.048 & \underline{0.023} & \underline{0.038} & 0.030 & 0.052 \\
& 25\%    & 0.028 & 0.051 & 0.030 & 0.055 & \underline{0.024} & \underline{0.040} & 0.030 & 0.054 \\
& 37.5\%  & 0.030 & 0.054 & 0.032 & 0.058 & \underline{0.027} & \underline{0.046} & 0.033 & 0.058 \\
& 50\%    & 0.030 & 0.052 & 0.033 & 0.058 & \underline{0.030} & \underline{0.050} & 0.035 & 0.062 \\
\bottomrule
\end{tabular}}
\end{table*}



\begin{table*}[htbp]
\centering
\caption{MSGBlock Network Structure Ablation Experiment (k=1, k=2, k=3, k=4)}
\renewcommand{\arraystretch}{1.2} % Adjust row height for better readability
\setlength{\tabcolsep}{4pt} % Adjust column spacing
\resizebox{1\linewidth}{!}{
\begin{tabular}{c|c|cc|cc|cc|cc}
\toprule
\multirow{2}{*}{\textbf{Dataset}} & \multirow{2}{*}{\textbf{Mask Ratio}} & \multicolumn{2}{c|}{\textbf{k=1}} & \multicolumn{2}{c|}{\textbf{k=2}} & \multicolumn{2}{c|}{\textbf{\underline{k=3}}} & \multicolumn{2}{c}{\textbf{k=4}} \\
 & & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} \\
\midrule
\multirow{4}{*}{ETTm1} 
& 12.5\%  & 0.015 & 0.081 & 0.015 & 0.079 & \underline{0.014} & \underline{0.078} & 0.015 & 0.079 \\
& 25\%    & 0.019 & 0.090 & 0.017 & 0.084 & \underline{0.016} & \underline{0.083} & 0.017 & 0.084 \\
& 37.5\%  & 0.024 & 0.102 & 0.020 & 0.092 & \underline{0.019} & \underline{0.089} & 0.020 & 0.092 \\
& 50\%    & 0.029 & 0.111 & 0.025 & 0.101 & \underline{0.023} & \underline{0.098} & 0.025 & 0.101 \\
\midrule
\multirow{4}{*}{ETTm2} 
& 12.5\%  & 0.017 & 0.074 & 0.016 & 0.072 & \underline{0.015} & \underline{0.068} & 0.016 & 0.072 \\
& 25\%    & 0.019 & 0.080 & 0.018 & 0.075 & \underline{0.017} & \underline{0.075} & 0.017 & 0.075 \\
& 37.5\%  & 0.021 & 0.086 & 0.020 & 0.080 & \underline{0.018} & \underline{0.078} & 0.019 & 0.078 \\
& 50\%    & 0.024 & 0.094 & 0.022 & 0.087 & \underline{0.021} & \underline{0.084} & 0.022 & 0.084 \\
\midrule
\multirow{4}{*}{ETTh1} 
& 12.5\%  & 0.032 & 0.122 & 0.029 & 0.116 & \underline{0.029} & \underline{0.115} & 0.031 & 0.119 \\
& 25\%    & 0.043 & 0.140 & 0.037 & 0.130 & \underline{0.036} & \underline{0.128} & 0.037 & 0.130 \\
& 37.5\%  & 0.053 & 0.156 & 0.047 & 0.146 & \underline{0.046} & \underline{0.144} & 0.046 & 0.145 \\
& 50\%    & 0.069 & 0.176 & 0.060 & 0.164 & \underline{0.058} & \underline{0.161} & 0.060 & 0.164 \\
\midrule
\multirow{4}{*}{ETTh2} 
& 12.5\%  & 0.034 & 0.115 & 0.033 & 0.114 & \underline{0.032} & \underline{0.110} & 0.032 & 0.110 \\
& 25\%    & 0.039 & 0.126 & 0.036 & 0.119 & \underline{0.036} & \underline{0.118} & 0.036 & 0.118 \\
& 37.5\%  & 0.043 & 0.134 & 0.040 & 0.127 & \underline{0.039} & \underline{0.124} & 0.040 & 0.125 \\
& 50\%    & 0.049 & 0.145 & 0.046 & 0.139 & \underline{0.045} & \underline{0.136} & 0.045 & 0.136 \\
\midrule
\multirow{4}{*}{Electricity} 
& 12.5\%  & 0.047 & 0.145 & 0.046 & 0.145 & \underline{0.046} & \underline{0.145} & 0.047 & 0.146 \\
& 25\%    & 0.053 & 0.154 & 0.053 & 0.155 & \underline{0.053} & \underline{0.155} & 0.054 & 0.156 \\
& 37.5\%  & 0.059 & 0.164 & 0.060 & 0.164 & \underline{0.061} & \underline{0.167} & 0.063 & 0.169 \\
& 50\%    & 0.066 & 0.172 & 0.068 & 0.175 & \underline{0.068} & \underline{0.175} & 0.069 & 0.177 \\
\midrule
\multirow{4}{*}{Weather} 
& 12.5\%  & 0.024 & 0.041 & 0.024 & 0.042 & \underline{0.023} & \underline{0.038} & 0.024 & 0.040 \\
& 25\%    & 0.027 & 0.043 & 0.027 & 0.047 & \underline{0.024} & \underline{0.040} & 0.027 & 0.041 \\
& 37.5\%  & 0.030 & 0.054 & 0.030 & 0.054 & \underline{0.027} & \underline{0.046} & 0.030 & 0.053 \\
& 50\%    & 0.030 & 0.052 & 0.030 & 0.058 & \underline{0.030} & \underline{0.050} & 0.030 & 0.051 \\
\bottomrule
\end{tabular}}
\end{table*}



\begin{table*}[htbp]
\centering
\caption{MSGBlock Network Structure Multi-Scale Convolution Kernel Size Ablation Experiment (Kernel Size=(3,1), Kernel Size=(3,3), Kernel Size=(5,3), Kernel Size=(13,3))}
\renewcommand{\arraystretch}{1.2} % Adjust row height for better readability
\setlength{\tabcolsep}{4pt} % Adjust column spacing
\resizebox{1\linewidth}{!}{
\begin{tabular}{c|c|cc|cc|cc|cc}
\toprule
\multirow{2}{*}{\textbf{Dataset}} & \multirow{2}{*}{\textbf{Mask Ratio}} & \multicolumn{2}{c|}{\textbf{Kernel Size=(3,1)}} & \multicolumn{2}{c|}{\textbf{Kernel Size=(3,3)}} & \multicolumn{2}{c|}{\textbf{Kernel Size=(5,3)}} & \multicolumn{2}{c}{\textbf{Kernel Size=(13,3)}} \\
 & & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} & \textbf{MSE} & \textbf{MAE} \\
\midrule
\multirow{4}{*}{ETTm1} 
& 12.5\%  & \underline{0.014} & \underline{0.078} & 0.017 & 0.088 & 0.017 & 0.088 & 0.028 & 0.114 \\
& 25\%    & \underline{0.016} & \underline{0.083} & 0.021 & 0.099 & 0.021 & 0.098 & 0.025 & 0.107 \\
& 37.5\%  & \underline{0.019} & \underline{0.089} & 0.022 & 0.100 & 0.023 & 0.098 & 0.028 & 0.113 \\
& 50\%    & \underline{0.023} & \underline{0.098} & 0.028 & 0.113 & 0.027 & 0.110 & 0.035 & 0.127 \\
\midrule
\multirow{4}{*}{ETTm2} 
& 12.5\%  & \underline{0.015} & \underline{0.068} & 0.016 & 0.068 & 0.017 & 0.071 & 0.018 & 0.075 \\
& 25\%    & \underline{0.017} & \underline{0.075} & 0.018 & 0.075 & 0.018 & 0.075 & 0.019 & 0.081 \\
& 37.5\%  & \underline{0.018} & \underline{0.078} & 0.020 & 0.080 & 0.019 & 0.079 & 0.021 & 0.086 \\
& 50\%    & \underline{0.021} & \underline{0.084} & 0.022 & 0.087 & 0.021 & 0.084 & 0.023 & 0.091 \\
\midrule
\multirow{4}{*}{ETTh1} 
& 12.5\%  & \underline{0.029} & \underline{0.115} & 0.033 & 0.125 & 0.035 & 0.129 & 0.042 & 0.142 \\
& 25\%    & \underline{0.036} & \underline{0.128} & 0.044 & 0.143 & 0.046 & 0.145 & 0.054 & 0.160 \\
& 37.5\%  & \underline{0.046} & \underline{0.144} & 0.053 & 0.157 & 0.055 & 0.159 & 0.068 & 0.178 \\
& 50\%    & \underline{0.058} & \underline{0.161} & 0.068 & 0.176 & 0.069 & 0.177 & 0.079 & 0.191 \\
\midrule
\multirow{4}{*}{ETTh2} 
& 12.5\%  & \underline{0.032} & \underline{0.110} & 0.033 & 0.114 & 0.032 & 0.110 & 0.035 & 0.120 \\
& 25\%    & \underline{0.036} & \underline{0.118} & 0.036 & 0.119 & 0.036 & 0.118 & 0.039 & 0.127 \\
& 37.5\%  & \underline{0.039} & \underline{0.124} & 0.040 & 0.127 & 0.041 & 0.124 & 0.044 & 0.135 \\
& 50\%    & \underline{0.045} & \underline{0.136} & 0.046 & 0.139 & 0.046 & 0.138 & 0.052 & 0.147 \\
\midrule
\multirow{4}{*}{Electricity} 
& 12.5\%  & \underline{0.046} & \underline{0.145} & 0.052 & 0.154 & 0.051 & 0.155 & 0.058 & 0.166 \\
& 25\%    & \underline{0.053} & \underline{0.155} & 0.060 & 0.166 & 0.060 & 0.165 & 0.067 & 0.177 \\
& 37.5\%  & \underline{0.061} & \underline{0.167} & 0.065 & 0.173 & 0.067 & 0.176 & 0.076 & 0.188 \\
& 50\%    & \underline{0.068} & \underline{0.175} & 0.073 & 0.182 & 0.075 & 0.186 & 0.082 & 0.196 \\
\midrule
\multirow{4}{*}{Weather} 
& 12.5\%  & \underline{0.023} & \underline{0.038} & 0.026 & 0.042 & 0.025 & 0.044 & 0.027 & 0.047 \\
& 25\%    & \underline{0.024} & \underline{0.040} & 0.027 & 0.047 & 0.026 & 0.047 & 0.034 & 0.058 \\
& 37.5\%  & \underline{0.027} & \underline{0.046} & 0.030 & 0.054 & 0.029 & 0.053 & 0.035 & 0.056 \\
& 50\%    & \underline{0.030} & \underline{0.050} & 0.030 & 0.058 & 0.034 & 0.061 & 0.032 & 0.055 \\
\bottomrule
\end{tabular}}
\end{table*}





\printcredits

%% Loading bibliography style file

\bibliographystyle{unsrt}
\bibliography{cas-refs}



%\vskip3pt

\bio{}

\endbio


\end{document}

