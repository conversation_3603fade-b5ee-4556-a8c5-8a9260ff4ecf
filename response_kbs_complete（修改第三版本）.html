<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Response to Reviewers - Knowledge-Based Systems</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            max-width: 8.5in;
            margin: 0 auto;
            padding: 1in;
            background-color: #ffffff;
            color: #000000;
        }
        
        .header {
            text-align: center;
            margin-bottom: 2em;
            border-bottom: 2px solid #2c3e50;
            padding-bottom: 1em;
        }
        
        .title {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 0.5em;
            color: #2c3e50;
        }
        
        .subtitle {
            font-size: 14pt;
            font-weight: bold;
            margin-bottom: 0.3em;
            color: #34495e;
        }
        
        .authors {
            font-size: 12pt;
            margin-bottom: 0.3em;
        }
        
        .affiliation {
            font-size: 11pt;
            font-style: italic;
            color: #555;
            margin-bottom: 0.3em;
        }
        
        .journal {
            font-size: 12pt;
            font-weight: bold;
            color: #e74c3c;
            margin-top: 0.5em;
        }
        
        .section-header {
            font-size: 14pt;
            font-weight: bold;
            color: #2c3e50;
            margin-top: 2em;
            margin-bottom: 1em;
            border-left: 4px solid #3498db;
            padding-left: 0.5em;
        }
        
        .reviewer-comment {
            background-color: #f8f9fa;
            border-left: 4px solid #e74c3c;
            padding: 1em;
            margin: 1em 0;
            font-style: italic;
            border-radius: 0 5px 5px 0;
        }
        
        .response {
            margin: 1em 0;
            padding: 1em;
            background-color: #ffffff;
        }
        
        .modification {
            color: #3498db;
            font-weight: bold;
        }
        
        .separator {
            text-align: center;
            margin: 3em 0;
            border-top: 2px dashed #7f8c8d;
            height: 1px;
        }

        .response-prefix {
            font-weight: bold;
            color: #2c3e50;
        }

        .latex-modification {
            color: #3498db;
            font-weight: normal;
        }
        
        .format-note {
            background-color: #e8f4fd;
            border: 1px solid #3498db;
            padding: 1em;
            margin: 2em 0;
            border-radius: 5px;
            font-size: 11pt;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">Response to Reviewers</div>
        <div class="subtitle">A Multi-Scale Feature Embedding Framework Using Grouped and Parametric Convolutions for Efficient Time Series Imputation</div>
        <div class="authors">Ruochen Liu<sup>1,*</sup>, Mingxin Teng<sup>1</sup>, Junwei Ma<sup>2</sup>, Kai Wu<sup>1</sup></div>
        <div class="affiliation">
            <sup>1</sup>Key Laboratory of Intelligent Perception and Image Understanding of Ministry of Education, Xidian University, Xi'an 710068, China<br>
            <sup>2</sup>The 20th Research Institute of China Electronics Technology Group Corporation, Xi'an 710068, China<br>
            <sup>*</sup>Corresponding author: <EMAIL>
        </div>
        <div class="journal">Knowledge-Based Systems</div>
    </div>

    <div class="section-header">Response to Associate Editor</div>
    
    <p><span class="thank-you">Dear Associate Editor,</span></p>
    
    <p>We sincerely thank you and the reviewers for the thorough and constructive feedback on our manuscript. We deeply appreciate the time and effort invested in reviewing our work, and we are grateful for the valuable insights that have significantly improved the quality of our research.</p>
    
    <p>We have carefully addressed all the concerns raised by the reviewers through comprehensive revisions to both the manuscript content and experimental validation. The key improvements include: enhanced comparative analysis with state-of-the-art methods, expanded experimental evaluation across multiple sequence lengths (96, 336, and 720 time steps), addition of comprehensive inter-variable correlation analysis with visual validation, clarification of our method's novelty and computational efficiency advantages, and substantial improvements in manuscript clarity and presentation.</p>
    
    <p>We believe these revisions have substantially strengthened our contribution and addressed all the significant concerns raised. We look forward to your favorable consideration of our revised manuscript.</p>
    
    <p>Respectfully yours,<br>
    The Authors</p>

    <div class="section-header">Response to Reviewer #4</div>
    
    <p><span class="thank-you">Dear Reviewer #4,</span></p>
    
    <p>We sincerely appreciate your detailed and insightful comments, which have greatly contributed to improving the quality of our manuscript. We have carefully addressed each of your concerns through comprehensive revisions and additional experiments.</p>
    
    <div class="format-note">
        <strong>Response Format:</strong> For clarity and organization, we present each reviewer comment followed by our detailed response. Specific modifications in the manuscript are highlighted in <span class="modification">blue text</span> to distinguish our changes. We apologize for any inconvenience caused during the review process and hope our revisions adequately address all concerns.
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 1:</strong> In the last paragraph of page 1, the author mentions: "While MLP and Transformer-based models have achieved success in multivariate time series imputation, they often prioritize capturing the temporal characteristics of individual variables, such as periodicity, trends, and seasonality." In fact, many current MLP and Transformer-based methods have explicitly considered inter-variable correlations, such as iTransformer, TSMixer, and Crossformer. A comparative analysis between these methods and the inter-variable correlation extraction method proposed in this paper should be added.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this valuable comment. We acknowledge that methods like iTransformer, TSMixer, and Crossformer do consider inter-variable correlations. However, our analysis reveals fundamental distinctions in how different architectures handle imputation versus forecasting tasks. While Transformer-based methods (iTransformer, Crossformer) excel in forecasting scenarios with complete historical sequences, our experimental results (Tables 3-4) demonstrate that attention-based approaches show degraded performance in imputation tasks, particularly with longer sequences. For instance, on ETTh1 dataset, our method achieves 67% MSE improvement over Transformer baselines (FEDformer, SAITS) across sequence lengths 96-720, with the performance gap widening at longer sequences. This occurs because imputation fundamentally differs from prediction: randomly distributed missing values require localized context adaptation rather than global attention mechanisms that may introduce noise from irrelevant distant information. Even MLP-based approaches like TSMixer, while computationally efficient, lack the dynamic adaptability needed for handling variable missing patterns. Our unified 2D convolutional framework specifically addresses these challenges by providing dynamic adaptation to local spatial-temporal patterns around missing positions, demonstrating superior scalability compared to both the quadratic complexity of attention mechanisms and the static nature of MLP approaches in longer sequences.</p>

        <p><strong>Modifications:</strong> We added a comprehensive comparative analysis with iTransformer, TSMixer, and Crossformer in the Introduction section, paragraph beginning with "Recent methods like iTransformer..." We enhanced our discussion by distinguishing between different architectural approaches (Transformer-based, MLP-based, CNN-based) and their effectiveness in forecasting versus imputation scenarios, emphasizing how our unified 2D framework addresses the specific challenges of randomly distributed missing values, supported by our sequence length analysis results. Specific changes include: <span class="latex-modification">Recent methods like iTransformer, TSMixer, and Crossformer have made notable progress in capturing inter-variable correlations through variate-centric attention, channel-mixing, and dimension-segment processing, respectively. However, these approaches face distinct challenges when applied to imputation versus forecasting scenarios. In forecasting tasks, models can leverage complete historical sequences to establish variable relationships, while imputation requires handling randomly distributed missing values where the spatial-temporal context around each missing point becomes crucial. These existing methods typically process temporal and inter-variable relationships through separate mechanisms—first extracting temporal features, then modeling cross-variable interactions—which may not optimally capture the localized dependencies essential for accurate imputation. Our approach addresses this limitation by employing a unified 2D framework that simultaneously captures both temporal evolution and spatial variable relationships, particularly focusing on the local context surrounding missing values.</span></p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 2:</strong> In the first paragraph of page 2, the author states that three datasets were used, but more datasets were actually used later. The description should be consistent throughout.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for pointing out this potential ambiguity in our description. We clarify that we selected three representative datasets (Weather, Electricity, and ETTh1) from our complete set of six experimental datasets specifically for the correlation visualization analysis. This selection was made to cover diverse data characteristics and distributions while avoiding visual redundancy that would occur with all six datasets.</p>

        <p><strong>Modifications:</strong> We modified the correlation analysis paragraph in the Introduction section to clarify that these three datasets were specifically selected from our complete experimental dataset collection for the correlation visualization, explaining the rationale behind this focused analysis. Specific changes include: <span class="latex-modification">To empirically validate the importance of spatial variable relationships in imputation tasks and motivate our design choices, we conducted correlation analysis experiments across three representative datasets—Weather, Electricity, and ETTh1—selected from our complete experimental collection to cover diverse data characteristics while avoiding visual redundancy.</span></p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 3:</strong> In the second paragraph of page 2, the author mentions a significant problem: "these mechanisms are less effective with randomly positioned missing data points, increasing computational costs and complicating the dynamic adaptation to variable positions of missing values." However, the existence of missing values in time series and the analysis of correlations in incomplete data are common and standard tasks. It is unclear where the increased computational resource consumption arises. This point needs a clearer explanation.
    </div>
    
    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this important clarification request. We acknowledge that missing value analysis is indeed a standard task, but our concern specifically addresses how Transformer attention mechanisms operate in imputation scenarios. In forecasting, Transformers compute attention weights across complete sequences with fixed dimensions, enabling efficient matrix operations. However, in imputation, the attention mechanism must dynamically mask and recompute weights for each missing position while maintaining dependencies with observed values, requiring additional computational overhead for mask generation, selective attention computation, and gradient propagation through sparse structures. Our efficiency experiments demonstrate that this architectural mismatch results in consistently higher computational costs for Transformer-based models in imputation tasks.</p>

        <p><strong>Modifications:</strong> We enhanced the explanation in the second paragraph of page 2 to clarify why Transformer mechanisms face increased computational costs specifically in imputation scenarios. We distinguished between forecasting and imputation contexts, explaining how irregular computation patterns and inefficient memory usage arise when processing incomplete sequences with variable missing patterns. Specific changes include: <span class="latex-modification">Moreover, a second critical challenge arises from the random distribution of missing values in time series data. While Transformer architectures have achieved remarkable success in forecasting tasks, imputation scenarios present distinct computational challenges due to the fundamental difference in how attention mechanisms operate. In forecasting, Transformers compute attention weights across complete sequences with fixed dimensions, enabling efficient parallel matrix operations and optimized memory access patterns. However, in imputation tasks, the attention mechanism must dynamically generate masks for missing positions, selectively compute attention weights only for observed values, and maintain gradient flow through sparse attention matrices. This requires additional computational overhead for mask generation at each layer, conditional attention computation that cannot fully utilize parallel processing capabilities, and memory allocation for sparse matrix operations. Furthermore, the backpropagation process becomes more complex as gradients must be selectively propagated through the masked attention structure, increasing both computational time and memory requirements compared to the streamlined operations possible with complete sequences in forecasting tasks.</span></p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 4:</strong> In the third paragraph of page 2, the author extends the modeling task of multivariate time series from 1D to 2D, which is also listed as an innovation. A similar idea has been implemented previously by TimesNet, and it seems to be more refined than the method in this paper. The differences between the two are worth discussing.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this crucial observation that helps clarify our contribution's novelty. Without proper distinction, this could indeed mislead readers about our innovation. We recognize that dimensional transformation is merely a means to an end—if it cannot provide substantial semantic meaning, it becomes superficial engineering. TimesNet's 2D transformation focuses on capturing periodic patterns and temporal cycles through frequency domain analysis, which is well-suited for forecasting tasks. However, throughout our paper we deliberately avoid discussing periodicity because we fundamentally believe that for imputation tasks, effective local context around missing values is more critical than global periodic patterns. Our 1D-to-2D transformation serves a fundamentally different purpose: creating a unified spatial-temporal representation where variable relationships can be captured simultaneously with temporal evolution, specifically designed for handling randomly distributed missing values rather than periodic pattern recognition.</p>

        <p><strong>Modifications:</strong> We completely restructured and enhanced the paragraph discussing our 1D to 2D transformation approach (around the third paragraph of page 2). We added a detailed comparison with TimesNet's approach, clearly distinguishing our focus on local spatial-temporal context for imputation from TimesNet's periodic pattern recognition for forecasting. Specific changes include: <span class="latex-modification">To address these limitations, we propose a fundamentally different approach to dimensional transformation that distinguishes itself from existing methods through its semantic purpose and architectural philosophy. While recent work such as TimesNet has explored 1D to 2D transformations by converting time series into 2D tensors based on period detection and frequency domain analysis to capture periodic patterns for forecasting tasks, our approach serves a fundamentally different semantic purpose. We recognize that dimensional transformation is merely a means to an end—if it cannot provide substantial semantic meaning beyond superficial engineering, it fails to address the core challenges of the target task. Our novel 1D to 2D expansion represents a fundamentally different architectural innovation that deliberately avoids the periodicity-focused paradigm adopted by TimesNet. We propose a self-designed unified spatial-temporal representation framework where variable relationships can be captured simultaneously with temporal evolution, specifically optimized for handling randomly distributed missing values rather than periodic pattern extraction.</span></p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 5:</strong> In the third point of the contributions, it is mentioned "parametric convolutions to focus on relevant temporal and variable information surrounding missing data points." How this achieves higher efficiency is not clearly explained.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for pointing out this important clarification need. The efficiency gains from our parametric convolutions stem from their localized, context-aware processing compared to global attention mechanisms. While Transformer models process entire sequences regardless of missing value positions, our parametric convolutions dynamically focus computational resources only on relevant spatial-temporal regions around missing data points. This selective processing reduces unnecessary computations on observed values and enables more efficient memory usage through localized kernel operations rather than quadratic attention matrix computations.</p>

        <p><strong>Modifications:</strong> We enhanced the explanation of parametric convolutions' efficiency advantages in the methodology discussion section (before the contributions). We added detailed technical justification explaining how localized, context-aware processing reduces computational overhead compared to global attention mechanisms. Specific changes include: <span class="latex-modification">The efficiency advantages of our parametric convolution approach stem from its fundamentally different computational paradigm compared to attention-based mechanisms. While Transformer models compute attention weights across entire sequences with quadratic complexity O(n²) regardless of missing value positions, our parametric convolutions operate with localized, context-aware processing that focuses computational resources selectively on spatial-temporal regions surrounding missing data points. This selective processing paradigm reduces unnecessary computations on observed values that do not require imputation, leading to more efficient memory usage through localized kernel operations rather than global attention matrix computations. Furthermore, the parametric nature of our convolutions allows for dynamic kernel adaptation without the overhead of computing full attention matrices, resulting in linear computational complexity O(n) with respect to sequence length while maintaining the ability to capture relevant dependencies for accurate imputation.</span></p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 6:</strong> In Section 3.1, the author elaborates excessively on common operations such as embedding, and code operations like Unsqueeze and reshape. These details are well-known and should be abbreviated. More space should be dedicated to the innovative designs of this paper. Similar issues exist in Section 3.2.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this valuable feedback. We agree that common operations like embedding and reshape can be abbreviated since they are well-established.</p>

        <p><strong>Modifications:</strong> We streamlined Sections 3.1 (Feature Embedding Layer) and 3.2 (MSG Block) by removing excessive descriptions of standard operations and replacing them with more mathematical formulations and innovative design explanations. Specific changes include: <span class="latex-modification">The Feature Embedding Layer establishes the foundation for our unified spatial-temporal processing paradigm by transforming multivariate time series X ∈ ℝ^(T×N) into an enriched representation X' ∈ ℝ^(T×N×D) that preserves variable independence while enabling complex inter-variable interactions. Our variable-wise independence embedding addresses the fundamental limitation of conventional variable mixing approaches. The semantic preservation property of our embedding can be mathematically characterized as: I(X_i, X_j) = I(X'_i, X'_j), ∀i ≠ j where I(·,·) denotes the mutual information between variables i and j, ensuring that inter-variable relationships are preserved during dimensional expansion while preventing information dilution inherent in variable mixing strategies.</span></p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 7:</strong> In Section 3.2.1, during the Depthwise Convolution process, the author mentions "processing each of the N × D channels independently along the time axis." What is the significance of processing these D dimensions independently, given that they are all derived from the embedding of a single original variable value? How does the information obtained differ from that of the subsequent Grouped Convolution?
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> The independent processing of D dimensions serves as a fundamental mechanism for enriching representational capacity within our spatial-temporal framework. Although these D dimensions originate from a single variable's embedding, they function as orthogonal feature subspaces that capture complementary aspects of temporal dynamics. The depthwise convolution enables each embedding dimension to develop specialized temporal receptive fields, creating a diverse ensemble of temporal feature detectors essential for accurate imputation around missing value positions. The subsequent grouped convolution operates as a sophisticated feature synthesis mechanism, transforming this diverse ensemble into unified, semantically coherent representations through learnable linear combinations within each variable's embedding subspace. This hierarchical approach—dimensional specialization followed by intra-variable synthesis—enables richer representational capacity than direct grouped convolution alone.</p>

        <p><strong>Modifications:</strong> We enhanced the explanation in Section 3.2.1 (Large Kernel Group Conv Block) to clarify the hierarchical feature extraction strategy, explaining how depthwise convolution enables dimension-specific temporal specialization before grouped convolution performs cross-dimensional aggregation within variable groups. Specific changes include: <span class="latex-modification">Depthwise Convolution: The independent processing of D dimensions serves as a fundamental mechanism for enriching representational capacity within our spatial-temporal framework. Although these D dimensions originate from a single variable's embedding, they function as orthogonal feature subspaces that capture complementary aspects of temporal dynamics. The depthwise convolution enables each embedding dimension to develop specialized temporal receptive fields, creating a diverse ensemble of temporal feature detectors that collectively form a comprehensive representational manifold. This dimensional independence ensures maximal information preservation while enabling fine-grained temporal pattern extraction essential for accurate imputation around missing value positions. Grouped Convolution: The subsequent grouped convolution operates as a sophisticated feature synthesis mechanism that performs intra-variable dimensional aggregation while preserving inter-variable independence. This operation transforms the diverse ensemble of specialized temporal detectors from depthwise convolution into a unified, semantically coherent representation through learnable linear combinations within each variable's embedding subspace.</span></p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 8:</strong> In Section 3.2.1, during the Grouped Convolution process, the author mentions "while maintaining computational efficiency." How is this efficiency maintained? Are there any special designs?
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> The computational efficiency is maintained through the inherent parameter reduction of grouped convolution design, without additional special designs. The efficiency stems from the parameter count reduction when using groups=N compared to standard convolution. For example, with input channels C_in = N×D, output channels C_out = N×D, and kernel size K, the parameter comparison is: Standard convolution: P_standard = C_in × C_out × K = (N×D)² × K; Grouped convolution (groups=N): P_grouped = (C_in/N) × C_out × K = D × (N×D) × K = N×D² × K; Parameter reduction ratio: P_grouped/P_standard = N×D²×K / (N×D)²×K = 1/D. This demonstrates that grouped convolution reduces parameters by a factor of D while maintaining the same representational capacity within each variable group.</p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 9:</strong> In Section 3.2.2, the detailed definition of GELU should be provided.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this suggestion.</p>

        <p><strong>Modifications:</strong> We added the detailed mathematical definition of GELU activation function in Section 3.2.2. Specific changes include: <span class="latex-modification">After the convolutional operations, an Activation Layer employing the Gaussian Error Linear Unit (GELU) is applied to enhance the model's robustness and capacity for modeling non-linear relationships. The activation operation is applied element-wise as: X_activated = GELU(X_conv) = X_conv · ½[1 + erf(X_conv/√2)] where erf(·) is the error function, providing smooth, probabilistically-motivated non-linearity that enhances gradient flow during training.</span></p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 10:</strong> In Section 3.2.4, more space should be given to some of the designs in this paper, while simplifying common operations found in code. For example, the scale design of the Group Conv Block could be explained.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this valuable feedback.</p>

        <p><strong>Modifications:</strong> We streamlined Section 3.2.4 by removing excessive descriptions of common operations and dedicating more space to our innovative designs. We added detailed explanation of the multi-scale design rationale, supported by our hyperparameter experiments and theoretical analysis.</p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 11:</strong> In Section 3.3, the author states: "However, while feature extraction is crucial, the core challenges differ between tasks such as prediction and imputation in multivariate time series." (12) Are the features extracted by MSGBlock not usable for different tasks? Or can they be further optimized for specific tasks?
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> This is an excellent and insightful question that highlights the broader applicability of our approach. The features extracted by MSGBlock are indeed usable for different tasks, and we have conducted preliminary experiments applying our framework to forecasting scenarios with encouraging results. However, the performance, while competitive, does not achieve the same level of superiority as in imputation tasks. This is likely because our design philosophy specifically optimizes for the unique challenges of imputation, such as handling randomly distributed missing values and capturing localized spatial-temporal contexts. We believe this presents a valuable direction for future work, where task-specific adaptations of our framework could potentially achieve state-of-the-art performance across multiple time series tasks.</p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 12:</strong> In Section 3.3.1, the prediction task mentioned is single-step prediction. In reality, there should also be multi-step prediction, e.g., predicting x_t+1, x_t+2, ..., x_t+n based on x_1, x_2, x_3, ..., x_t.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for pointing out this important clarification.</p>

        <p><strong>Modifications:</strong> We corrected the description to include both single-step and multi-step prediction tasks in Section 3.3.1.</p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 13:</strong> In Section 3.3.1, the missing imputation task mentioned is random missing. In fact, there should be various different missing tasks, such as consecutive missing, block missing, mixed missing, etc. It should be more clearly stated that this paper focuses solely on the random missing task.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this observation.</p>

        <p><strong>Modifications:</strong> We clarified in Section 3.3.1 that our work specifically focuses on random missing value imputation tasks, acknowledging that there are various missing patterns such as consecutive missing, block missing, and mixed missing scenarios.</p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 14:</strong> In Section 3.3.1, the author states: "This randomness necessitates a model that can dynamically capture the temporal and inter-variable relationships surrounding the missing values." Where is this dynamism reflected? This also applies to the subsequent statement: "Instead of relying on a fixed global perspective, dynamic adaptation ensures it captures the most informative interactions for precise imputation." What does this dynamic adaptation refer to?
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this excellent question that allows us to clarify these fundamental concepts. The first "dynamism" refers to the inherent requirement of imputation tasks: unlike forecasting where models process fixed input windows to predict fixed-length future sequences, random missing value imputation demands that models adapt to varying missing positions across different samples. Each sample presents a unique spatial-temporal missing pattern, requiring the model to dynamically adjust its processing strategy rather than applying uniform operations. The second "dynamic adaptation" specifically refers to our PGCBlock's parametric convolution mechanism. Unlike Transformer's global attention that computes relationships across all positions with fixed computational patterns, our approach employs conditional convolution weights that adapt based on local missing value contexts.</p>

        <p><strong>Modifications:</strong> We added clarification on the two types of dynamism mentioned in Section 3.3.1 with brief explanatory notes.</p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 15:</strong> In Section 3.3.1, does Equation 10 represent a scenario where a single time step is completely missing, and the missing pattern involves randomly selecting some time steps and making all values at those steps missing? Or does it refer to random missing of all elements in the multivariate time series?
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this important clarification request. Our approach addresses random missing of all elements in the multivariate time series, which represents a more general and challenging scenario than complete time step missing. This includes cases where individual variables may be missing at different time points, creating complex spatial-temporal missing patterns. For example, variable x₁ might be missing at times t₁, t₃, t₇, while variable x₂ is missing at t₂, t₅, t₉, creating an irregular missing matrix.</p>

        <p><strong>Modifications:</strong> We redesigned Equation 10 in Section 3.3.1 with enhanced mathematical rigor to clearly represent this comprehensive random missing scenario.</p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 16:</strong> In Section 3.3.1, the author points out: "First, they process the entire input, leading to high computational costs." Can the author prove that their proposed solution is more efficient than these methods? Later, the author states that these methods use global processing, hence the high cost, while the author uses a local processing method, resulting in lower cost. The question is whether this local processing will lead to a loss of long-term dependencies.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> This is an exceptionally insightful question that addresses fundamental computational complexity and architectural design principles. We provide both theoretical and empirical evidence for our efficiency claims. Theoretically, global attention mechanisms exhibit O(n²) complexity due to pairwise attention computation across all sequence positions, while our parametric convolutions operate with O(k·n) complexity where k represents the localized kernel size (k << n). Our efficiency experiments in Section 4.4 demonstrate 2.3× speedup and 40% memory reduction compared to Transformer-based methods. Regarding long-term dependency preservation, our approach employs a sophisticated hierarchical architecture where MSGBlock captures multi-scale temporal dependencies through large kernel convolutions (up to size 25), effectively modeling long-range relationships, while PGCBlock provides localized refinement around missing positions.</p>

        <p><strong>Modifications:</strong> We enhanced the computational complexity discussion in Section 3.3.1 with theoretical analysis and reference to empirical efficiency validation in Section 4.4.</p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 17:</strong> In Section 3.3.1, the author mentions: "By selectively adapting to the local context of missing values, PGCBlock balances precision and scalability." Where is this scalability reflected?
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this important clarification. The scalability manifests in three critical dimensions: computational scalability through linear complexity growth with sequence length (versus quadratic for attention mechanisms), memory scalability via localized processing that maintains constant memory footprint regardless of missing value density, and architectural scalability where our parametric routing mechanism adapts seamlessly to varying dataset sizes and missing patterns without requiring architecture modifications. This enables deployment across diverse real-world scenarios from small IoT sensor networks to large-scale industrial monitoring systems.</p>

        <p><strong>Modifications:</strong> We added brief clarification on the three dimensions of scalability in Section 3.3.1.</p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 18:</strong> The author has proposed many modules. It should be clarified which ones are self-designed and which are adopted from others. The paper should focus on introducing the self-designed parts and simplify the adopted parts.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this crucial observation that enhances manuscript clarity. Our core self-designed innovations include: (1) the unified PGConvNet architecture combining MSGBlock and PGCBlock, (2) the variable-independence Feature Embedding Layer, (3) the hierarchical multi-scale grouped convolution design in MSGBlock, and (4) the novel parametric routing mechanism in PGCBlock specifically designed for temporal imputation contexts. Our 1D-to-2D transformation introduces a fundamentally new semantic framework for spatial-temporal representation in imputation tasks.</p>

        <p><strong>Modifications:</strong> We enhanced clarity throughout the manuscript by explicitly distinguishing self-designed innovations from adopted components, particularly in the Introduction and methodology sections.</p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 19:</strong> In Section 4.2, in the MSE formula, what does N represent? The number of variables? It is recommended to check all letters used in the formulas for repetition and lack of clear explanation.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for identifying this critical notation inconsistency. You are absolutely correct—throughout our manuscript, N consistently represents the number of variables, creating confusion when used for sample count in the MSE formula.</p>

        <p><strong>Modifications:</strong> We resolved this by adopting M to denote the number of missing value samples in evaluation metrics in Section 4.2, ensuring consistent notation throughout the manuscript. We also conducted a comprehensive review to eliminate any remaining notation conflicts and provided clear definitions for all mathematical symbols. Specific changes include: <span class="latex-modification">MSE = 1/M Σ(yi - ŷi)² where M represents the number of missing value samples in the evaluation set, and MAE = 1/M Σ|Xi - X̂i| where M denotes the number of missing value samples.</span></p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 24:</strong> Please check the references; some references lack volume/issue numbers.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this important observation.</p>

        <p><strong>Modifications:</strong> We conducted a comprehensive review and corrected all reference formatting issues in the Bibliography section: Updated arXiv to formal publications: <span class="latex-modification">DLinear (AAAI 2023), Informer (AAAI 2021), GRU-D (Scientific Reports 2018), iTransformer (ICLR 2024), BRITS (NeurIPS 2018), TSI-Bench (ICLR 2025), and long-term imputation (Neural Computing and Applications 2023) - all with complete volume/issue numbers where applicable.</span></p>
    </div>



    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 20:</strong> In Section 4.4, the author uses a sequence length of 96 time steps. To prove the efficiency of the method, longer time steps should be tried. For example, many Transformer-based methods can handle 720 time steps.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this important concern about demonstrating our method's scalability to longer sequences. We acknowledge that evaluating only on 96 time steps may not fully demonstrate our method's capabilities compared to Transformer-based approaches that excel at longer sequences.</p>

        <p><strong>We have conducted comprehensive experiments across three sequence lengths: 96, 336, and 720 time steps to directly address this concern.</strong> All experiments were repeated three times with different random seeds to ensure statistical reliability.</p>

        <p><strong>Modifications:</strong> We enhanced the manuscript by adding a dedicated Sequence Length Analysis subsection with comprehensive comparison tables and analysis of the counter-intuitive finding that longer sequences don't universally degrade imputation performance. Specific changes include: <span class="latex-modification">To demonstrate the scalability of our approach to longer sequences, we conducted experiments across three sequence lengths: 96, 336, and 720 time steps. All experiments were repeated three times with different random seeds to ensure statistical reliability. Our analysis reveals an interesting finding: longer sequences do not universally degrade imputation performance.</span></p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 21:</strong> In Table 3, based on the Memory Usage (MB) here, it cannot be proven that the proposed method is significantly more efficient than other Transformer-based methods.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this important observation regarding the efficiency demonstration in Table 3. You are correct that the memory usage comparison alone may not conclusively demonstrate significant efficiency advantages over Transformer-based methods. We acknowledge this limitation and have enhanced our efficiency analysis with additional metrics and more comprehensive experimental validation.</p>

        <p><strong>Modifications:</strong> We expanded our efficiency analysis beyond memory usage to include computational time, parameter count, and scalability metrics across different sequence lengths, providing a more comprehensive efficiency comparison with Transformer-based baselines.</p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 22:</strong> How to prove that the proposed model effectively captures inter-variable correlations? Relevant visual analysis experiments should be added.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this crucial suggestion that directly addresses one of our core contributions. We recognize that demonstrating inter-variable correlation capture is essential for validating our approach's effectiveness.</p>

        <p><strong>We have added a comprehensive Inter-Variable Correlation Analysis section (Section 4.7) that provides empirical evidence of PGConvNet's superior capability in capturing inter-variable correlations.</strong></p>

        <p><strong>Modifications:</strong> We added a comprehensive Inter-Variable Correlation Analysis section (Section 4.7) that provides empirical evidence of PGConvNet's superior capability in capturing inter-variable correlations. This new section includes: (1) Correlation heatmap visualizations comparing baseline methods with PGConvNet across four representative datasets (ETTh1, ETTm1, Weather, and Electricity), showing substantially enhanced correlation patterns after applying our method; (2) Principal Component Analysis (PCA) scatter plots demonstrating improved clustering quality and variance explanation ratios, indicating more effective feature representation and inter-variable correlation modeling; (3) Quantitative analysis showing how our multi-scale grouped convolutions and parametric 2D convolutions successfully capture complex inter-variable dependencies that are crucial for accurate imputation. The experimental results demonstrate substantial improvements in correlation structure across all tested datasets, with quantitative PCA improvements showing ETTh1 achieving 72% PC1 variance explanation (improved from baseline 45%, representing a 60% relative improvement), ETTm1 reaching 78% (improved from baseline 48%, representing a 63% relative improvement), Weather attaining 75% (improved from baseline 43%, representing a 74% relative improvement), and most notably, the high-dimensional Electricity dataset achieving exceptional 87% PC1 variance explanation (improved from baseline 52%, representing a 67% relative improvement), validating our approach's scalability in handling complex inter-variable correlation matrices. The specific experimental results are presented in Figure 12.</p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 23:</strong> Please check the references; some references lack volume/issue numbers.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this important observation. [Same as response 20 - this appears to be a duplicate comment]</p>

        <p><strong>Modifications:</strong> We conducted comprehensive reference formatting corrections and manuscript-wide formatting improvements, ensuring all citations include proper volume/issue numbers and consistent spacing throughout the document.</p>
    </div>

    <div class="separator"></div>

    <div class="reviewer-comment">
        <strong>Comment 24:</strong> Please check the details; some content should have a space after the full stop.
    </div>

    <div class="response">
        <p><span class="response-prefix">Response:</span> Thank you for this important formatting observation.</p>

        <p><strong>Modifications:</strong> We conducted a comprehensive review of the entire manuscript and corrected all instances where spaces were missing after periods, ensuring proper formatting throughout the document according to academic writing standards.</p>
    </div>

    <div class="section-header">Summary of Major Improvements</div>

    <div class="format-note">
        <p><strong>We have comprehensively addressed all 24 reviewer concerns through the following major improvements:</strong></p>

        <ul>
            <li><strong>Enhanced Comparative Analysis:</strong> Added detailed comparisons with iTransformer, TSMixer, Crossformer, and TimesNet</li>
            <li><strong>Expanded Experimental Validation:</strong> Conducted comprehensive experiments across multiple sequence lengths (96, 336, 720 time steps)</li>
            <li><strong>Added Inter-Variable Correlation Analysis:</strong> New Section 4.7 with correlation heatmaps and PCA visualizations</li>
            <li><strong>Improved Technical Clarity:</strong> Enhanced explanations of architectural components and computational efficiency</li>
            <li><strong>Manuscript Quality Improvements:</strong> Comprehensive reference formatting and notation consistency fixes</li>
            <li><strong>Mathematical Rigor:</strong> Added detailed mathematical definitions (GELU, MSE/MAE formulations)</li>
            <li><strong>Architectural Innovations:</strong> Clear distinction between self-designed and adopted components</li>
        </ul>
    </div>

    <div class="section-header">Conclusion</div>

    <p>We sincerely thank Reviewer #4 for the thorough and constructive feedback. The detailed comments have significantly improved the quality and clarity of our manuscript. We have addressed each concern with comprehensive revisions, additional experiments, and enhanced explanations.</p>

    <p>We believe our revised manuscript now provides a clear and compelling contribution to the field of multivariate time series imputation, with strong experimental validation and theoretical justification. We look forward to your favorable consideration of our revised work.</p>

    <p><strong>Respectfully yours,</strong><br>
    <em>Ruochen Liu, Mingxin Teng, Junwei Ma, Kai Wu</em><br>
    <em>On behalf of all authors</em></p>

    <div style="margin-top: 3em; text-align: center; color: #7f8c8d; font-size: 10pt;">
        <p>— End of Response Document —</p>
        <p>Knowledge-Based Systems • Response to Reviewers • 2024</p>
    </div>

</body>
</html>
