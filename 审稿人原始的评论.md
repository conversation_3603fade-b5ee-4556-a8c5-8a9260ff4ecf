Associate Editor: Thank you for submitting your manuscript to Knowledge-Based Systems. The reviewers find the proposed two-stage framework for multivariate time series imputation potentially valuable, but they raise significant concerns that must be addressed before further consideration.
副主编：感谢您将稿件提交给 Knowledge-Based Systems。审稿人发现所提出的多元时间序列插补的两阶段框架可能有价值，但他们提出了在进一步考虑之前必须解决的重大问题。

Key issues include the need for clearer articulation of the method’s novelty relative to existing models like TimesNet and iTransformer, justification for architectural choices such as depthwise and grouped convolutions, and stronger explanations for the model’s claimed computational efficiency and dynamic adaptability. The experimental setup should be expanded to include diverse missing patterns and longer sequence lengths, and visual analyses are needed to validate the model’s ability to capture inter-variable correlations. In addition, the manuscript would benefit from improved clarity, more concise descriptions of standard operations, and consistent dataset reporting.
关键问题包括需要更清楚地阐明该方法相对于 TimesNet 和 iTransformer 等现有模型的新颖性，为深度卷积和分组卷积等架构选择提供理由，以及对模型声称的计算效率和动态适应性进行更有力的解释。应扩展实验设置以包括不同的缺失模式和更长的序列长度，并且需要视觉分析来验证模型捕获变量间相关性的能力。此外，手稿将受益于更高的清晰度、对标准作的更简洁的描述以及一致的数据集报告。

We invite you to revise the manuscript thoroughly, addressing all comments in a detailed and structured response. We look forward to reviewing your revised submission.
我们邀请您彻底修改稿件，以详细且结构化的回复解决所有评论。我们期待审核您修改后提交的内容。

Reviewer #4: This manuscript proposes a novel two-stage network architecture to address the problem of missing value imputation in multivariate time series. My comments are as follows:
审稿人 #4：本稿提出了一种新颖的两阶段网络架构，以解决多变量时间序列中的缺失值插补问题。我的评论如下：
(1) In the last paragraph of page 1, the author mentions: "While MLP and Transformer-based models have achieved success in multivariate time series imputation, they often prioritize capturing the temporal characteristics of individual variables, such as periodicity, trends, and seasonality." In fact, many current MLP and Transformer-based methods have explicitly considered inter-variable correlations, such as iTransformer, TSMixer, and Crossformer. A comparative analysis between these methods and the inter-variable correlation extraction method proposed in this paper should be added.
（1）在第 1 页的最后一段中，作者提到：“虽然基于 MLP 和 Transformer 的模型在多变量时间序列插补方面取得了成功，但它们通常优先考虑捕获单个变量的时间特征，例如周期性、趋势和季节性。事实上，许多当前基于 MLP 和 Transformer 的方法都明确考虑了变量间相关性，例如 iTransformer、TSMixer 和 Crossformer。应增加这些方法与本文提出的变量间相关提取方法的比较分析。
(2) In the first paragraph of page 2, the author states that three datasets were used, but more datasets were actually used later. The description should be consistent throughout.
（2）在第2页的第一段中，作者指出使用了三个数据集，但后来实际使用了更多的数据集。描述应始终保持一致。
(3) In the second paragraph of page 2, the author mentions a significant problem: "these mechanisms are less effective with randomly positioned missing data points, increasing computational costs and complicating the dynamic adaptation to variable positions of missing values." However, the existence of missing values in time series and the analysis of correlations in incomplete data are common and standard tasks. It is unclear where the increased computational resource consumption arises. This point needs a clearer explanation.
（3）在第2页的第二段中，作者提到了一个重大问题：“这些机制在随机定位的缺失数据点的情况下效果较差，增加了计算成本，并使对缺失值的可变位置的动态适应变得复杂。然而，时间序列中缺失值的存在和不完整数据中相关性的分析是常见的标准任务。目前尚不清楚增加的计算资源消耗从何而来。这一点需要更清楚的解释。
(4) In the third paragraph of page 2, the author extends the modeling task of multivariate time series from 1D to 2D, which is also listed as an innovation. A similar idea has been implemented previously by TimesNet, and it seems to be more refined than the method in this paper. The differences between the two are worth discussing.
（4）在第 2 页的第三段中，作者将多元时间序列的建模任务从一维扩展到二维，这也被列为一项创新。TimesNet 之前已经实现了类似的想法，它似乎比本文中的方法更精细。两者之间的区别值得讨论。
(5) In the third point of the contributions, it is mentioned "parametric convolutions to focus on relevant temporal and variable information surrounding missing data points." How this achieves higher efficiency is not clearly explained.
（5） 在贡献的第三点中，提到了“参数卷积，以关注围绕缺失数据点的相关时间和可变信息”。这如何实现更高的效率没有明确解释。
(6) In Section 3.1, the author elaborates excessively on common operations such as embedding, and code operations like Unsqueeze and reshape. These details are well-known and should be abbreviated. More space should be dedicated to the innovative designs of this paper. Similar issues exist in Section 3.2.
（6） 在第 3.1 节中，作者对嵌入等常见作以及 Unsqueeze 和 reshape 等代码作进行了过多的阐述。这些细节是众所周知的，应该进行缩写。应该有更多的篇幅专门用于本文的创新设计。第 3.2 节中也存在类似的问题。
(7) In Section 3.2.1, during the Depthwise Convolution process, the author mentions "processing each of the N × D channels independently along the time axis." What is the significance of processing these D dimensions independently, given that they are all derived from the embedding of a single original variable value? How does the information obtained differ from that of the subsequent Grouped Convolution?
（7）在第 3.2.1 节中，在深度卷积过程中，作者提到“沿时间轴独立处理 N×D 通道中的每一个”。鉴于这些 D 维度都是从单个原始变量值的嵌入中得出的，独立处理它们有什么意义？获得的信息与后续分组卷积的信息有何不同？
(8) In Section 3.2.1, during the Grouped Convolution process, the author mentions "while maintaining computational efficiency." How is this efficiency maintained? Are there any special designs?
（8） 在第 3.2.1 节中，在分组卷积过程中，作者提到“在保持计算效率的同时”。如何保持这种效率？有什么特别的设计吗？
(9) In Section 3.2.2, the detailed definition of GELU should be provided.
（9） 在第 3.2.2 节中，应提供 GELU 的详细定义。


(10) In Section 3.2.4, more space should be given to some of the designs in this paper, while simplifying common operations found in code. For example, the scale design of the Group Conv Block could be explained.
（10） 在第 3.2.4 节中，应该为本文中的一些设计留出更多空间，同时简化代码中的常见作。例如，可以解释 Group Conv Block 的比例设计。
(11) In Section 3.3, the author states: "However, while feature extraction is crucial, the core challenges differ between tasks such as prediction and imputation in multivariate time series." (12) Are the features extracted by MSGBlock not usable for different tasks? Or can they be further optimized for specific tasks?
（11）在第 3.3 节中，作者指出：“然而，虽然特征提取至关重要，但多元时间序列中的预测和插补等任务之间的核心挑战是不同的。（12）MSGBlock 提取的特征是否不能用于不同的任务？或者它们可以针对特定任务进一步优化吗？


(13) In Section 3.3.1, the prediction task mentioned is single-step prediction. In reality, there should also be multi-step prediction, e.g., predicting x_t+1, x_t+2, ..., x_t+n based on x_1, x_2, x_3, ..., x_t.
（13） 在第 3.3.1 节中，提到的预测任务是单步预测。实际上，还应该有多步预测，例如，根据 x_1、x_2、x_3、...、x_t 预测 x_t+1、x_t+2、...、x_t+n。


(14) In Section 3.3.1, the missing imputation task mentioned is random missing. In fact, there should be various different missing tasks, such as consecutive missing, block missing, mixed missing, etc. It should be more clearly stated that this paper focuses solely on the random missing task.
（14） 在第 3.3.1 节中，提到的缺失插补任务是随机缺失的。其实应该有各种不同的缺失任务，比如连续缺失、块缺失、混合缺失等。应该更明确地指出，本文只关注随机缺失任务。


(15) In Section 3.3.1, the author states: "This randomness necessitates a model that can dynamically capture the temporal and inter-variable relationships surrounding the missing values." Where is this dynamism reflected? This also applies to the subsequent statement: "Instead of relying on a fixed global perspective, dynamic adaptation ensures it captures the most informative interactions for precise imputation." What does this dynamic adaptation refer to?
（15） 在第 3.3.1 节中，作者指出：“这种随机性需要一个能够动态捕获围绕缺失值的时间和变量间关系的模型。这种活力体现在哪里？这也适用于随后的陈述：“动态适应不是依赖固定的全局视角，而是确保它捕获信息最丰富的交互以进行精确插补。这种动态适应指的是什么？
(16) In Section 3.3.1, does Equation 10 represent a scenario where a single time step is completely missing, and the missing pattern involves randomly selecting some time steps and making all values at those steps missing? Or does it refer to random missing of all elements in the multivariate time series?
（16） 在第 3.3.1 节中，公式 10 是否表示一个时间步长完全缺失，并且缺失模式涉及随机选择一些时间步长并使这些步长处的所有值缺失？还是指多元时间序列中所有元素的随机缺失？
(17) In Section 3.3.1, the author points out: "First, they process the entire input, leading to high computational costs." Can the author prove that their proposed solution is more efficient than these methods? Later, the author states that these methods use global processing, hence the high cost, while the author uses a local processing method, resulting in lower cost. The question is whether this local processing will lead to a loss of long-term dependencies.
（17） 在第 3.3.1 节中，作者指出：“首先，它们处理整个输入，导致计算成本高。作者能否证明他们提出的解决方案比这些方法更有效？后来，作者指出，这些方法使用全局处理，因此成本高，而作者使用局部处理方法，导致成本较低。问题是这种本地处理是否会导致长期依赖关系的丢失。
(18) In Section 3.3.1, the author mentions: "By selectively adapting to the local context of missing values, PGCBlock balances precision and scalability." Where is this scalability reflected?
（18） 在第 3.3.1 节中，作者提到：“通过有选择地适应缺失值的局部上下文，PGCBlock 平衡了精度和可扩展性。这种可扩展性体现在哪里？
(19) The author has proposed many modules. It should be clarified which ones are self-designed and which are adopted from others. The paper should focus on introducing the self-designed parts and simplify the adopted parts.
（19）作者提出了许多模块。应该明确哪些是自己设计的，哪些是从别人那里借来的。本文应重点介绍自行设计的零件，并简化采用的零件。
(20) In Section 4.2, in the MSE formula, what does N represent? The number of variables? It is recommended to check all letters used in the formulas for repetition and lack of clear explanation.
（20） 在第 4.2 节中，在 MSE 公式中，N 代表什么？变量的数量？建议检查公式中使用的所有字母是否重复和缺乏清晰的解释。


(21) In Section 4.4, the author uses a sequence length of 96 time steps. To demonstrate the efficiency of the method, longer time steps should be attempted. For example, many Transformer-based methods can handle 720 time steps.
（21） 在第 4.4 节中，作者使用了 96 个时间步长的序列长度。为了证明该方法的效率，应尝试更长的时间步长。例如，许多基于 Transformer 的方法可以处理 720 个时间步长。


(22) In Table 3, based on the Memory Usage (MB) here, it cannot be proven that the proposed method is significantly more efficient than other Transformer-based methods.
（22）在表 3 中，根据这里的内存使用率（MB），无法证明所提出的方法比其他基于 Transformer 的方法效率更高。


(23) How to prove that the proposed model effectively captures inter-variable correlations? Relevant visual analysis experiments should be added.
（23）如何证明所提出的模型有效地捕获了变量间相关性？应增加相关的视觉分析实验。


(24) Please check the references; some references lack volume/issue numbers.
（24） 请检查参考文献;一些参考文献缺少卷/期号。
(25) Please check the details; some content should have a space after the full stop.
（25） 请检查详细信息;某些内容应在句号后有一个空格。