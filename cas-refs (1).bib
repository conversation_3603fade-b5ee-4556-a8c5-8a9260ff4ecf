


@inproceedings{DLinear_AAAI2023,
  title={Are Transformers Effective for Time Series Forecasting?},
  author={<PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON> and <PERSON><PERSON>},
  booktitle={Proceedings of the Thirty-Seventh {AAAI} Conference on Artificial Intelligence, {AAAI} 2023, Washington, DC, USA, February 7-14, 2023},
  volume={37},
  number={9},
  pages={11121--11128},
  publisher={{AAAI} Press},
  year={2023},
  doi={10.1609/aaai.v37i9.26317},
  url={https://ojs.aaai.org/index.php/AAAI/article/view/26317}
}

@inproceedings{informer,
  title={Informer: Beyond Efficient Transformer for Long Sequence Time-Series Forecasting},
  author={<PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON><PERSON> and <PERSON>, <PERSON> and <PERSON>, <PERSON><PERSON> and <PERSON><PERSON>, <PERSON> and <PERSON>, <PERSON>},
  booktitle={Proceedings of the AAAI Conference on Artificial Intelligence},
  volume={35},
  number={12},
  pages={11106--11115},
  year={2021},
  doi={10.1609/aaai.v35i12.17325}
}

@article{GRU-D,
  title={Recurrent Neural Networks for Multivariate Time Series with Missing Values},
  author={<PERSON><PERSON>, <PERSON> and <PERSON>urushotham, <PERSON><PERSON> and <PERSON>, Kyunghyun and Sontag, David and Liu, <PERSON>},
  journal={Scientific Reports},
  volume={8},
  number={1},
  pages={6085},
  year={2018},
  publisher={Nature Publishing Group},
  doi={10.1038/s41598-018-24271-9}
}

@misc{Rlinear,
      title={Revisiting Long-term Time Series Forecasting: An Investigation on Linear Mapping}, 
      author={Zhe Li and Shiyi Qi and Yiduo Li and Zenglin Xu},
      year={2023},
      eprint={2305.10721},
      archivePrefix={arXiv},
      primaryClass={cs.LG},
      url={https://arxiv.org/abs/2305.10721},  
}


@inproceedings{PatchTST,
  title     = {A Time Series is Worth 64 Words: Long-term Forecasting with Transformers},
  author    = {Nie, Yuqi and
               H. Nguyen, Nam and
               Sinthong, Phanwadee and
               Kalagnanam, Jayant},
  booktitle = {International Conference on Learning Representations},
  year      = {2023},
  url       = {https://openreview.net/forum?id=Jbdc0vTOcol}
}
@inproceedings{Crossformer,
title={Crossformer: Transformer Utilizing Cross-Dimension Dependency for Multivariate Time Series Forecasting},
author={Yunhao Zhang and Junchi Yan},
booktitle={International Conference on Learning Representations},
year={2023},
url={https://openreview.net/forum?id=vSVLM2j9eie}
}
@inproceedings{fedformer,
  title={{FEDformer}: Frequency enhanced decomposed transformer for long-term series forecasting},
  author={Zhou, Tian and Ma, Ziqing and Wen, Qingsong and Wang, Xue and Sun, Liang and Jin, Rong},
  booktitle={Proceedings of the 39th International Conference on Machine Learning},
  volume={162},
  pages={27268--27286},
  year={2022},
  publisher={PMLR}
}
@inproceedings{Autoformer,
  title={Autoformer: Decomposition Transformers with {Auto-Correlation} for Long-Term Series Forecasting},
  author={Haixu Wu and Jiehui Xu and Jianmin Wang and Mingsheng Long},
  booktitle={Advances in Neural Information Processing Systems},
  volume={34},
  year={2021},
  publisher={Curran Associates, Inc.}
}
@misc{tcn,
  title={An Empirical Evaluation of Generic Convolutional and Recurrent Networks for Sequence Modeling},
  author={Bai, Shaojie and Kolter, J. Zico and Koltun, Vladlen},
  year={2018},
  eprint={1803.01271},
  archivePrefix={arXiv},
  primaryClass={cs.LG},
  url={https://arxiv.org/abs/1803.01271}
}
@inproceedings{timesnet,
  title={TimesNet: Temporal 2D-Variation Modeling for General Time Series Analysis},
  author={Haixu Wu and Tengge Hu and Yong Liu and Hang Zhou and Jianmin Wang and Mingsheng Long},
  booktitle={International Conference on Learning Representations},
  year={2023},
  url={https://openreview.net/forum?id=ju_Uqw384Oq}
}
@inproceedings{ModerTcn,
title={Modern{TCN}: A Modern Pure Convolution Structure for General Time Series Analysis},
author={Luo, Donghao and Wang, Xue},
booktitle={The Twelfth International Conference on Learning Representations},
year={2024},
url={https://openreview.net/forum?id=vpJMJerXHU}
}
@inproceedings{yang2019condconv,
  title={CondConv: Conditionally Parameterized Convolutions for Efficient Inference},
  author={Yang, Brandon and Bender, Gabriel and Le, Quoc and Ngiam, Jiquan},
  booktitle={Advances in Neural Information Processing Systems},
  volume={32},
  year={2019},
  publisher={Curran Associates, Inc.}
}
@inproceedings{micn,
  title={MICN: Multi-scale Local and Global Context Modeling for Long-term Series Forecasting},
  author={Huiqiang Wang and Jian Peng and Feihu Huang and Jince Wang and Junhui Chen and Yifei Xiao},
  booktitle={International Conference on Learning Representations},
  year={2023},
  url={https://openreview.net/forum?id=zt53IDUR1U}
}
@inproceedings{liu2022SCINet,
title={SCINet: Time Series Modeling and Forecasting with Sample Convolution and Interaction},
author={Liu, Minhao and Zeng, Ailing and Chen, Muxi and Xu, Zhijian and Lai, Qiuxia and Ma, Lingna and Xu, Qiang},
booktitle={Advances in Neural Information Processing Systems},
volume={35},
pages={5816--5828},
year={2022},
publisher={Curran Associates, Inc.}
}
@article{LightTS2023,
  title={Less Is More: Fast Multivariate Time Series Forecasting with Light Sampling-oriented MLP Structures},
  author={Zhou, Xintian and Zeng, Ailing and Liu, Yanfei and Liu, Haixu and Xu, Zhiguang},
  journal={arXiv preprint arXiv:2302.12721},
  year={2023}
}
@article{li2023mtsmixers,
  title={MTS-Mixers: Multivariate Time Series Forecasting via Factorized Temporal and Channel Mixing},
  author={Li, Zhe and Rao, Zhongwen and Pan, Lujia and Xu, Zenglin},
  journal={arXiv preprint arXiv:2302.04501},
  year={2023}
}

@article{rnn,
  title={Finding structure in time},
  author={Elman, Jeffrey L},
  journal={Cognitive science},
  volume={14},
  number={2},
  pages={179--211},
  year={1990},
  publisher={Wiley Online Library}
}
@inproceedings{transformer,
  title={Attention is all you need},
  author={Vaswani, Ashish and Shazeer, Noam and Parmar, Niki and Uszkoreit, Jakob and Jones, Llion and Gomez, Aidan N and Kaiser, Łukasz and Polosukhin, Illia},
  booktitle={Advances in neural information processing systems},
  volume={30},
  pages={5998--6008},
  year={2017}
}
@article{mlp,
  title={Learning representations by back-propagating errors},
  author={Rumelhart, David E and Hinton, Geoffrey E and Williams, Ronald J},
  journal={Nature},
  volume={323},
  number={6088},
  pages={533--536},
  year={1986},
  publisher={Nature Publishing Group}
}


@article{islam2013financial,
  title={Financial development and energy consumption nexus in Malaysia: a multivariate time series analysis},
  author={Islam, Faridul and Shahbaz, Muhammad and Ahmed, Ashraf U and Alam, Md Mahmudul},
  journal={Economic modelling},
  volume={30},
  pages={435--441},
  year={2013},
  publisher={Elsevier}
}

@article{health,
  title={Clustering analysis of aging diseases and chronic habits with multivariate time series electrocardiogram and medical records},
  author={Tseng, Kuo-Kun and Li, Jiaqian and Tang, Yih-Jing and Yang, Ching-Wen and Lin, Fang-Ying and Zhao, Zhaowen},
  journal={Frontiers in Aging Neuroscience},
  volume={12},
  pages={95},
  year={2020},
  publisher={Frontiers Media SA}
}

@article{environment,
  title={Revealing the organization of complex adaptive systems through multivariate time series modeling},
  author={Angeler, David G and Drakare, Stina and Johnson, Richard K},
  journal={Ecology and Society},
  volume={16},
  number={3},
  year={2011},
  publisher={JSTOR}
}
@article{sensor,
  title={Using trend clusters for spatiotemporal interpolation of missing data in a sensor network},
  author={Appice, Annalisa and Ciampi, Anna and Malerba, Donato and Guccione, Pietro},
  journal={Journal of Spatial Information Science},
  volume={2013},
  number={6},
  pages={119--153},
  year={2013}
}

@article{Mobile,
  title={One Model to Find Them All Deep Learning for Multivariate Time-Series Anomaly Detection in Mobile Network Data},
  author={Gonz{\'a}lez, G Garc{\'\i}a and Tagliafico, S Martinez and Fern{\'a}ndez, Alicia and G{\'o}mez, Gabriel and Casas, P and others},
  journal={IEEE Transactions on Network and Service Management},
  volume={21},
  number={2},
  year={2024},
  publisher={IEEE},
  doi={10.1109/TNSM.2023.3340146}
}

@article{interpolation,
  title={Interpolation of signals with missing data using Principal Component Analysis},
  author={Oliveira, Paulo and Gomes, L},
  journal={Multidimensional Systems and Signal Processing},
  volume={21},
  pages={25--43},
  year={2010},
  publisher={Springer}
}
@article{saeipourdizaj2021application,
  title={Application of imputation methods for missing values of PM10 and O3 data: Interpolation, moving average and K-nearest neighbor methods},
  author={Saeipourdizaj, Parisa and Sarbakhsh, Parvin and Gholampour, Akbar},
  journal={Environmental Health Engineering and Management Journal},
  volume={8},
  number={3},
  pages={215--226},
  year={2021},
  publisher={Environmental Health Engineering And Management Journal}
}

@misc{wang2024tssurvey,
  title={Deep Time Series Models: A Comprehensive Survey and Benchmark},
  author={Yuxuan Wang and Haixu Wu and Jiaxiang Dong and Yong Liu and Mingsheng Long and Jianmin Wang},
  year={2024},
  eprint={2407.13278},
  archivePrefix={arXiv},
  primaryClass={cs.LG},
  url={https://arxiv.org/abs/2407.13278}
}

@article{chan1997sequential,
  title={Sequential linear interpolation of multidimensional functions},
  author={Chan, JZ and Allebach, Jan P and Bouman, Charles A},
  journal={IEEE Transactions on Image Processing},
  volume={6},
  number={9},
  pages={1231--1245},
  year={1997},
  publisher={IEEE}
}

@article{flornes1999direct,
  title={A direct interpolation method for irregular sampling},
  author={Flornes, Kristin M and Lyubarskii, Yurii and Seip, Kristian},
  journal={Applied and Computational Harmonic Analysis},
  volume={7},
  number={3},
  pages={305--314},
  year={1999},
  publisher={Elsevier}
}

@inproceedings{cabodi2011interpolation,
  title={Interpolation sequences revisited},
  author={Cabodi, Gianpiero and Nocco, Sergio and Quer, Stefano},
  booktitle={2011 Design, Automation \& Test in Europe},
  pages={316--322},
  year={2011},
  organization={IEEE}
}

@article{chow1971best,
  title={Best linear unbiased interpolation, distribution, and extrapolation of time series by related series},
  author={Chow, Gregory C and Lin, An-loh},
  journal={The Review of Economics and Statistics},
  volume={53},
  number={4},
  pages={372--375},
  year={1971},
  publisher={MIT Press}
}

@article{carrizosa2013time,
  title={Time series interpolation via global optimization of moments fitting},
  author={Carrizosa, Emilio and Olivares-Nadal, Alba V and Ram{\'\i}rez-Cobo, Pepa},
  journal={European Journal of Operational Research},
  volume={230},
  number={1},
  pages={97--112},
  year={2013},
  publisher={Elsevier}
}

@article{DU,
title = {SAITS: Self-attention-based imputation for time series},
journal = {Expert Systems with Applications},
volume = {219},
pages = {119619},
year = {2023},
issn = {0957-4174},
doi = {https://doi.org/10.1016/j.eswa.2023.119619},
url = {https://www.sciencedirect.com/science/article/pii/S0957417423001203},
author = {Wenjie Du and David Côté and Yan Liu},
keywords = {Time series, Missing values, Imputation model, Self-attention, Neural network},
abstract = {Missing data in time series is a pervasive problem that puts obstacles in the way of advanced analysis. A popular solution is imputation, where the fundamental challenge is to determine what values should be filled in. This paper proposes SAITS, a novel method based on the self-attention mechanism for missing value imputation in multivariate time series. Trained by a joint-optimization approach, SAITS learns missing values from a weighted combination of two diagonally-masked self-attention (DMSA) blocks. DMSA explicitly captures both the temporal dependencies and feature correlations between time steps, which improves imputation accuracy and training speed. Meanwhile, the weighted-combination design enables SAITS to dynamically assign weights to the learned representations from two DMSA blocks according to the attention map and the missingness information. Extensive experiments quantitatively and qualitatively demonstrate that SAITS outperforms the state-of-the-art methods on the time-series imputation task efficiently and reveal SAITS’ potential to improve the learning performance of pattern recognition models on incomplete time-series data from the real world.}
}

@inproceedings{du2024tsibenchbenchmarkingtimeseries,
  title={TSI-Bench: Benchmarking Time Series Imputation},
  author={Du, Wenjie and Wang, Jun and Qian, Linglong and Yang, Yiyuan and Ibrahim, Zina and Liu, Fanxing and Wang, Zepu and Liu, Haoxin and Zhao, Zhiyuan and Zhou, Yingjie and Wang, Wenjia and Ding, Kaize and Liang, Yuxuan and Prakash, B. Aditya and Wen, Qingsong},
  booktitle={The Thirteenth International Conference on Learning Representations},
  year={2025},
  url={https://openreview.net/forum?id=eDJsL1qAxw}
}



@article{SRI,
title = {A generic sparse regression imputation method for time series and tabular data},
journal = {Knowledge-Based Systems},
volume = {279},
pages = {110965},
year = {2023},
issn = {0950-7051},
doi = {https://doi.org/10.1016/j.knosys.2023.110965},
url = {https://www.sciencedirect.com/science/article/pii/S0950705123007153},
author = {Athanasios I. Salamanis and George A. Gravvanis and Sotiris Kotsiantis and Konstantinos M. Giannoutakis},
keywords = {Missing data imputation, Regression, Discretization, Sparse least squares},
abstract = {Although many missing data imputation methods have been proposed in the relevant literature, they focus on either time series or tabular data, but not on both. Hence, a generic sparse regression method for missing data imputation is proposed. The imputed values of a target feature are generated by solving a sparse least squares problem using a preconditioned iterative method based on generic approximate sparse pseudoinverse. Sparsity is introduced by dummy encoding existing or constructed (through discretization) categorical features. Extensive experiments were conducted on several datasets, and the results demonstrate the effectiveness of the method for both time series and tabular data.}
}

@article{Granulardataimputation,
title = {Granular data imputation: A framework of Granular Computing},
journal = {Applied Soft Computing},
volume = {46},
pages = {307-316},
year = {2016},
issn = {1568-4946},
doi = {https://doi.org/10.1016/j.asoc.2016.05.006},
url = {https://www.sciencedirect.com/science/article/pii/S1568494616302071},
author = {Chunfu Zhong and Witold Pedrycz and Dan Wang and Lina Li and Zhiwu Li},
keywords = {Data imputation, Granular Computing, Reconstruction, Granular data, Principle of justifiable granularity, Fuzzy clustering},
abstract = {Data imputation is a common practice encountered when dealing with incomplete data. Irrespectively of the existing spectrum of techniques, the results of imputation are commonly numeric meaning that once the data have been imputed they are not distinguishable from the original data being initially available prior to imputation. In this study, the crux of the proposed approach is to develop a way of representing imputed (missing) entries as information granules and in this manner quantify the quality of the imputation process and the quality of the ensuing data. We establish a two-stage imputation mechanism in which we start with any method of numeric imputation and then form a granular representative of missing value. In this sense, the approach could be regarded as an enhancement of the existing imputation techniques. Proceeding with the detailed imputation schemes, we discuss two ways of imputation. In the first one, imputation is realized for individual variables of data sets and afterwards enhanced by the buildup of information granules. In the second approach, we are concerned with the use of fuzzy clustering, Fuzzy C-Means (FCM), which helps establish a structure in the data and then use this information in the imputation process. The design of information granules invokes the fundamentals of Granular Computing, namely a principle of justifiable granularity and an allocation of information granularity. Numeric experiments concerned with a suite of publicly available data sets offer detailed insights into the main facets of the overall design process and deliver a parametric analysis of the methods.}
}

@article{OMDCI,
title = {Optimised multiple data partitions for cluster-wise imputation of missing values in gene expression data},
journal = {Expert Systems with Applications},
volume = {257},
pages = {125040},
year = {2024},
issn = {0957-4174},
doi = {https://doi.org/10.1016/j.eswa.2024.125040},
url = {https://www.sciencedirect.com/science/article/pii/S0957417424019079},
author = {Simon Yosboon and Natthakan Iam-On and Tossapon Boongoen and Phimmarin Keerin and Khwunta Kirimasthong},
keywords = {Gene expression, Missing value, Imputation, Ensemble clustering, Swarm intelligence},
abstract = {It is commonly agreed that the quality of data analysis may be degraded by the presence of missing data. In various domains such as bioinformatics, an effective tool is required for the discovery of knowledge from gene expression datasets. One may simply ignore defected samples, while others attempt to either make an algorithm robust to the problem or develop an imputation technique to fill in missing values. This research focuses on the latter and introduces a new hybridisation of cluster- and neighbour-based references to generate an accurate estimate. It also proposes a novel exploitation of multiple clusterings as the source of cluster-wise information, instead of a single data partition that has been studied by existing methods. These data partitions are selected from a pool of base clusterings with respect to both quality and diversity criteria. Another hybridisation is thus established between swarm intelligence and this search problem. In particular, the algorithm of artificial bee colony (ABC) is explored, with two new operators being invented to allow an evolution of solutions, or food sources for bees. Also, two different imputation strategies are provided to generate entries of missing entries in a data matrix, called cluster-only and cluster-neighbour. Based on published gene expression datasets and different experimental settings, the resulting models usually outperform their baselines and recent approaches, which make use of cluster analysis or devise an intelligent determination of nearest neighbours. Furthermore, they have proven competitive to a benchmark technique belonging to the global approach, especially with high missing ratios. Further extensions to iterative refinement and supervised imputation are discussed in addition to parameter analysis.}
}

@article{GBB,
title = {Generative broad Bayesian (GBB) imputer for missing data imputation with uncertainty quantification},
journal = {Knowledge-Based Systems},
volume = {301},
pages = {112272},
year = {2024},
issn = {0950-7051},
doi = {https://doi.org/10.1016/j.knosys.2024.112272},
url = {https://www.sciencedirect.com/science/article/pii/S0950705124009067},
author = {Sin-Chi Kuok and Ka-Veng Yuen and Tim Dodwell and Mark Girolami},
keywords = {Bayesian inference, broad Bayesian learning, Imputation, Missing data, Uncertainty quantification},
abstract = {Generative broad Bayesian (GBB) imputer, a novel nonparametric data-driven tool for missing data imputation with uncertainty quantification, is proposed. The proposed imputer aims to generate missing data in an iterative manner based on an augmentable broad Bayesian learning network. The procedure consists of the preparatory and tuning phase. The preparatory phase provides preliminary imputation of the missing data to develop a complete dataset. The tuning phase refines the accuracy of the imputation results based on the augmented learning network. There are three appealing features of the proposed GBB imputer: (i) the nonparametric generative scheme provides a universal tool for missing data imputation without constraints on the type of data attribution, missing data pattern, or requirement of the prior information about the dataset; (ii) the quantified uncertainty of the imputation results reflects the associated reliability and provides a rational termination indicator for the iterative imputation procedure; and (iii) the learning network can be augmented progressively to adopt architectural reconfigurations based on the inherited information of the trained network for efficient imputation. To demonstrate the efficacy and applicability of the proposed GBB imputer, we present two simulated examples under various scenarios and a case study with the achieved in-situ seismic records of the 2016 Mw6.5 Norcia earthquake.}
}

@article{MVI,
title = {A novel and efficient risk minimisation-based missing value imputation algorithm},
journal = {Knowledge-Based Systems},
volume = {304},
pages = {112435},
year = {2024},
issn = {0950-7051},
doi = {https://doi.org/10.1016/j.knosys.2024.112435},
url = {https://www.sciencedirect.com/science/article/pii/S0950705124010694},
author = {Yu-Lin He and Jia-Yin Yu and Xu Li and Philippe Fournier-Viger and Joshua Zhexue Huang},
keywords = {Missing value imputation, Risk minimisation, Autoencoder neural network, Consistent probability distribution, Maximum mean discrepancy},
abstract = {Missing value imputation (MVI) is a key task in data science, in which learning models are built from incomplete data. In contrast to externally driven MVI algorithms, this study proposes a novel risk minimisation-based MVI algorithm (RM-MVI) that considers both the internal characteristics of missing data and the external performance for specific classification applications. RM-MVI is technically designed for labelled data and is applied in two stages: filling with structural risk minimisation (SRM) and refining with empirical risk minimisation (ERM). In the filling stage, an autoencoder with a single hidden layer is trained on the original dataset without missing values. Missing values are first initialised with random numbers, and the imputation values are then preliminarily optimised based on the derived updating rule to minimise the structural risk-oriented objective function. After the imputation values have been preliminarily optimised in the filling stage, a neural-network-based classifier is trained in the refining stage to optimise the imputation values sophisticatedly by reducing the empirical risk. Experiments were conducted on several benchmark datasets to validate the feasibility, rationality, and effectiveness of the proposed RM-MVI algorithm. The results show that (1) the optimisation processes of the imputation values corresponding to the SRM and ERM are convergent so that the optimised imputation values can be obtained; (2) SRM can ensure distribution consistency of the imputation values that are preliminarily optimised in the filling stage, while ERM can optimise the imputation values sophisticatedly in the refining stage, which is more helpful for classifier training; and (3) the RM-MVI algorithm can yield considerably better MVI performance on benchmark datasets than 11 well-known MVI algorithms, such as a 26% higher distribution consistency ratio and 2% to 5% higher testing accuracies for 6 classifiers on average. This demonstrates that RM-MVI is a viable approach for addressing MVI problems.}
}

@article{LSTM,
  title={Long Short-Term Memory},
  author={Hochreiter, Sepp and Schmidhuber, J{\"u}rgen},
  journal={Neural Computation},
  volume={9},
  number={8},
  pages={1735--1780},
  year={1997},
  publisher={MIT Press}
}

@misc{GRU,
  title={Empirical Evaluation of Gated Recurrent Neural Networks on Sequence Modeling},
  author={Chung, Junyoung and Gulcehre, Caglar and Cho, Kyunghyun and Bengio, Yoshua},
  year={2014},
  eprint={1412.3555},
  archivePrefix={arXiv},
  primaryClass={cs.LG}
}


@inproceedings{TSMixer, series={KDD ’23},
   title={TSMixer: Lightweight MLP-Mixer Model for Multivariate Time Series Forecasting},
   url={http://dx.doi.org/10.1145/3580305.3599533},
   DOI={10.1145/3580305.3599533},
   booktitle={Proceedings of the 29th ACM SIGKDD Conference on Knowledge Discovery and Data Mining},
   publisher={ACM},
   author={Ekambaram, Vijay and Jati, Arindam and Nguyen, Nam and Sinthong, Phanwadee and Kalagnanam, Jayant},
   year={2023},
   month=aug, pages={459–469},
   collection={KDD ’23} }



@inproceedings{iTransformer,
  title={iTransformer: Inverted Transformers Are Effective for Time Series Forecasting},
  author={Yong Liu and Tengge Hu and Haoran Zhang and Haixu Wu and Shiyu Wang and Lintao Ma and Mingsheng Long},
  booktitle={The Twelfth International Conference on Learning Representations, {ICLR} 2024, Vienna, Austria, May 7-11, 2024},
  publisher={OpenReview.net},
  year={2024},
  url={https://openreview.net/forum?id=JePfAI8fah}
}


@inproceedings{brits,
  title={BRITS: Bidirectional Recurrent Imputation for Time Series},
  author={Cao, Wei and Wang, Dong and Li, Jian and Zhou, Hao and Li, Lei and Li, Yitan},
  booktitle={Advances in Neural Information Processing Systems},
  volume={31},
  year={2018},
  publisher={Curran Associates, Inc.}
}




@article{multiscale_mlp_mixer,
  title={A Multi-Scale Decomposition MLP-Mixer for Time Series Analysis},
  author={Zhong, Shuhan and Song, Sizhe and Zhuo, Weipeng and Li, Guanyao and Liu, Yang and Chan, S.-H. Gary},
  journal={Proceedings of the VLDB Endowment},
  volume={17},
  number={7},
  pages={1723--1736},
  year={2024},
  publisher={VLDB Endowment}
}

@article{longterm_imputation,
  title={Long-term missing value imputation for time series data using deep neural networks},
  author={Park, Jangho and Muller, Juliane and Arora, Bhavna and Faybishenko, Boris and Pastorello, Gilberto and Varadharajan, Charuleka and Sahu, Reetik and Agarwal, Deborah},
  journal={Neural Computing and Applications},
  volume={35},
  number={12},
  pages={8595--8610},
  year={2023},
  publisher={Springer}
}